<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 账户管理</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .account-list {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 0 16px 16px;
            overflow: hidden;
        }
        
        .account-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .account-item:last-child {
            border-bottom: none;
        }
        
        .account-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            color: white;
        }
        
        .account-info {
            flex: 1;
        }
        
        .account-name {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .account-type {
            font-size: 14px;
            color: var(--ios-secondary-label);
        }
        
        .account-balance {
            text-align: right;
            margin-right: 12px;
        }
        
        .balance-amount {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .balance-label {
            font-size: 12px;
            color: var(--ios-secondary-label);
        }
        
        .account-actions {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .action-button {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
        }
        
        .edit-button {
            background: var(--ios-blue);
            color: white;
        }
        
        .delete-button {
            background: var(--ios-red);
            color: white;
        }
        
        .add-account {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 0 16px 16px;
            padding: 16px;
            text-align: center;
            border: 2px dashed var(--ios-separator);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .add-account:hover {
            border-color: var(--ios-blue);
            background: var(--ios-gray6);
        }
        
        .add-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            background: var(--ios-gray5);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 20px;
            color: var(--ios-gray);
        }
        
        .add-text {
            font-size: 16px;
            color: var(--ios-secondary-label);
        }
        
        .stats-card {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 16px;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            text-align: center;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--ios-secondary-label);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="account.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title">账户管理</div>
                <div class="navbar-right">
                    <a href="add-account.html" class="navbar-button">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 统计信息 -->
                <div class="stats-card">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value text-primary" id="totalAccounts">0</div>
                            <div class="stat-label">账户数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value text-success" id="totalAssets">¥0</div>
                            <div class="stat-label">总资产</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value text-danger" id="totalDebt">¥0</div>
                            <div class="stat-label">总负债</div>
                        </div>
                    </div>
                </div>

                <!-- 账户列表 -->
                <div class="account-list" id="accountList">
                    <!-- 账户将通过JavaScript动态加载 -->
                </div>

                <!-- 添加账户 -->
                <div class="add-account" onclick="location.href='add-account.html'">
                    <div class="add-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="add-text">添加新账户</div>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="data-manager.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadAccounts();
            loadStatistics();
        });

        function loadAccounts() {
            const accountList = document.getElementById('accountList');
            const accounts = dataManager.getAccounts();
            
            accountList.innerHTML = '';
            
            accounts.forEach(account => {
                const accountElement = document.createElement('div');
                accountElement.className = 'account-item';
                
                const typeText = getAccountTypeText(account.type);
                const cardInfo = account.cardNumber ? ` ${account.cardNumber}` : '';
                
                accountElement.innerHTML = `
                    <div class="account-icon" style="background: ${account.color};">
                        <i class="${account.icon}"></i>
                    </div>
                    <div class="account-info">
                        <div class="account-name">${account.name}</div>
                        <div class="account-type">${typeText}${cardInfo}</div>
                    </div>
                    <div class="account-balance">
                        <div class="balance-amount ${account.balance >= 0 ? 'text-success' : 'text-danger'}">
                            ¥${Math.abs(account.balance).toFixed(2)}
                        </div>
                        <div class="balance-label">${account.isDefault ? '默认账户' : '余额'}</div>
                    </div>
                    <div class="account-actions">
                        <button class="action-button edit-button" onclick="editAccount('${account.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${!account.isDefault ? `
                            <button class="action-button delete-button" onclick="deleteAccount('${account.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                `;
                
                // 点击账户查看详情
                accountElement.addEventListener('click', function(e) {
                    if (!e.target.closest('.account-actions')) {
                        viewAccountDetails(account.id);
                    }
                });
                
                accountList.appendChild(accountElement);
            });
        }

        function loadStatistics() {
            const accounts = dataManager.getAccounts();
            const totalAccounts = accounts.length;
            const totalAssets = accounts.filter(acc => acc.balance > 0).reduce((sum, acc) => sum + acc.balance, 0);
            const totalDebt = Math.abs(accounts.filter(acc => acc.balance < 0).reduce((sum, acc) => sum + acc.balance, 0));
            
            document.getElementById('totalAccounts').textContent = totalAccounts;
            document.getElementById('totalAssets').textContent = `¥${totalAssets.toFixed(2)}`;
            document.getElementById('totalDebt').textContent = `¥${totalDebt.toFixed(2)}`;
        }

        function getAccountTypeText(type) {
            const typeMap = {
                'bank_card': '储蓄卡',
                'credit_card': '信用卡',
                'e_wallet': '电子钱包',
                'cash': '现金账户'
            };
            return typeMap[type] || '其他';
        }

        function editAccount(accountId) {
            window.location.href = `add-account.html?edit=${accountId}`;
        }

        function deleteAccount(accountId) {
            const account = dataManager.getAccount(accountId);
            if (!account) return;

            if (account.isDefault) {
                alert('默认账户不能删除');
                return;
            }

            if (confirm(`确定要删除账户"${account.name}"吗？\n删除后相关的交易记录将无法正常显示账户信息。`)) {
                try {
                    dataManager.deleteAccount(accountId);
                    loadAccounts();
                    loadStatistics();

                    // 显示成功提示
                    const toast = document.createElement('div');
                    toast.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: rgba(0, 0, 0, 0.8);
                        color: white;
                        padding: 12px 20px;
                        border-radius: 8px;
                        font-size: 14px;
                        z-index: 1000;
                    `;
                    toast.textContent = '账户已删除';
                    document.body.appendChild(toast);

                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 2000);
                } catch (error) {
                    alert(error.message || '删除失败，请重试');
                    console.error('删除账户失败:', error);
                }
            }
        }

        function viewAccountDetails(accountId) {
            window.location.href = `account-details.html?id=${accountId}`;
        }

        // 监听数据变化
        window.addEventListener('dataChanged', function() {
            loadAccounts();
            loadStatistics();
        });
    </script>
</body>
</html>
