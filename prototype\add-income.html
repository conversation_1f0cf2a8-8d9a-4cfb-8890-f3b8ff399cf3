<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 添加收入</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .form-section {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 16px;
            overflow: hidden;
        }
        
        .form-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .form-item:last-child {
            border-bottom: none;
        }
        
        .form-label {
            width: 80px;
            font-size: 16px;
            color: var(--ios-label);
        }
        
        .form-input {
            flex: 1;
            border: none;
            font-size: 16px;
            color: var(--ios-label);
            background: transparent;
            text-align: right;
        }
        
        .form-input:focus {
            outline: none;
        }
        
        .form-input::placeholder {
            color: var(--ios-tertiary-label);
        }
        
        .amount-input {
            font-size: 24px;
            font-weight: 600;
            color: var(--ios-green);
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            padding: 20px;
        }
        
        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 8px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }
        
        .category-item:hover {
            background: var(--ios-gray6);
        }
        
        .category-item.selected {
            border-color: var(--ios-green);
            background: rgba(52, 199, 89, 0.1);
        }
        
        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: 8px;
            color: white;
        }
        
        .category-name {
            font-size: 12px;
            text-align: center;
            color: var(--ios-label);
        }
        
        .account-selector {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }
        
        .selected-account {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .account-icon {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }
        
        .save-button {
            margin: 16px;
            background: var(--ios-green);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: calc(100% - 32px);
        }
        
        .save-button:disabled {
            background: var(--ios-gray5);
            color: var(--ios-gray);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="record.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title" id="pageTitle">添加收入</div>
                <div class="navbar-right">
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 金额输入 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">金额</div>
                        <input type="number" class="form-input amount-input" id="amountInput" placeholder="0.00" step="0.01">
                    </div>
                </div>

                <!-- 分类选择 -->
                <div class="form-section">
                    <div class="form-item" style="border-bottom: none; padding-bottom: 8px;">
                        <div class="form-label">分类</div>
                    </div>
                    <div class="category-grid" id="categoryGrid">
                        <!-- 分类将通过JavaScript动态加载 -->
                    </div>
                </div>

                <!-- 账户选择 -->
                <div class="form-section">
                    <div class="form-item" id="accountSelector">
                        <div class="form-label">账户</div>
                        <div class="account-selector">
                            <div class="selected-account" id="selectedAccount">
                                <span>请选择账户</span>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--ios-gray2);"></i>
                        </div>
                    </div>
                </div>

                <!-- 备注 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">备注</div>
                        <input type="text" class="form-input" id="noteInput" placeholder="添加备注">
                    </div>
                </div>

                <!-- 日期时间 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">日期</div>
                        <input type="datetime-local" class="form-input" id="dateInput">
                    </div>
                </div>

                <!-- 保存按钮 -->
                <button class="save-button" id="saveButton" disabled>保存收入记录</button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="data-manager.js"></script>
    <script>
        let selectedCategoryId = null;
        let selectedAccountId = null;
        let editingTransactionId = null;

        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否是编辑模式
            const urlParams = new URLSearchParams(window.location.search);
            editingTransactionId = urlParams.get('edit');

            if (editingTransactionId) {
                document.getElementById('pageTitle').textContent = '编辑收入';
                loadTransactionForEdit(editingTransactionId);
            }

            loadIncomeCategories();
            loadAccounts();
            initializeDateTime();
            bindEvents();
        });

        function loadIncomeCategories() {
            const categoryGrid = document.getElementById('categoryGrid');
            const incomeCategories = dataManager.getCategories('income');
            
            categoryGrid.innerHTML = '';
            incomeCategories.forEach(category => {
                const categoryElement = document.createElement('div');
                categoryElement.className = 'category-item';
                categoryElement.dataset.categoryId = category.id;
                categoryElement.innerHTML = `
                    <div class="category-icon" style="background: ${category.color};">
                        <i class="${category.icon}"></i>
                    </div>
                    <div class="category-name">${category.name}</div>
                `;
                
                categoryElement.addEventListener('click', function() {
                    selectCategory(category.id);
                });
                
                categoryGrid.appendChild(categoryElement);
            });
        }

        function loadAccounts() {
            const accounts = dataManager.getAccounts();
            if (accounts.length > 0) {
                // 默认选择第一个账户
                selectAccount(accounts[0].id);
            }
        }

        function selectCategory(categoryId) {
            // 清除之前的选择
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 选择新分类
            const categoryElement = document.querySelector(`[data-category-id="${categoryId}"]`);
            if (categoryElement) {
                categoryElement.classList.add('selected');
                selectedCategoryId = categoryId;
                validateForm();
            }
        }

        function selectAccount(accountId) {
            const account = dataManager.getAccount(accountId);
            if (account) {
                selectedAccountId = accountId;
                const selectedAccountElement = document.getElementById('selectedAccount');
                selectedAccountElement.innerHTML = `
                    <div class="account-icon" style="background: ${account.color};">
                        <i class="${account.icon}"></i>
                    </div>
                    <span>${account.name}</span>
                `;
                validateForm();
            }
        }

        function loadTransactionForEdit(transactionId) {
            const transaction = dataManager.getTransaction(transactionId);
            if (!transaction || transaction.type !== 'income') {
                alert('交易记录不存在或类型不匹配');
                window.location.href = 'details.html';
                return;
            }

            // 填充表单数据
            document.getElementById('amountInput').value = transaction.amount;
            document.getElementById('noteInput').value = transaction.note || '';

            // 设置日期
            const date = new Date(transaction.date);
            const localDateTime = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
                .toISOString().slice(0, 16);
            document.getElementById('dateInput').value = localDateTime;

            // 设置选中的分类和账户
            selectedCategoryId = transaction.categoryId;
            selectedAccountId = transaction.accountId;

            // 更新保存按钮文本
            document.getElementById('saveButton').textContent = '更新收入记录';
        }

        function initializeDateTime() {
            // 只在非编辑模式下设置当前时间
            if (!editingTransactionId) {
                const dateInput = document.getElementById('dateInput');
                const now = new Date();
                const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
                    .toISOString().slice(0, 16);
                dateInput.value = localDateTime;
            }
        }

        function bindEvents() {
            const amountInput = document.getElementById('amountInput');
            const saveButton = document.getElementById('saveButton');
            const accountSelector = document.getElementById('accountSelector');

            amountInput.addEventListener('input', validateForm);

            accountSelector.addEventListener('click', function() {
                showAccountSelector();
            });

            saveButton.addEventListener('click', function() {
                saveIncomeRecord();
            });
        }

        function showAccountSelector() {
            const accounts = dataManager.getAccounts();
            const accountOptions = accounts.map(account => 
                `<option value="${account.id}" ${account.id === selectedAccountId ? 'selected' : ''}>
                    ${account.name} (¥${account.balance.toFixed(2)})
                </option>`
            ).join('');

            const accountId = prompt('选择账户:\n' + accounts.map((acc, index) => 
                `${index + 1}. ${acc.name} (¥${acc.balance.toFixed(2)})`
            ).join('\n') + '\n\n请输入序号:');

            if (accountId && accountId >= 1 && accountId <= accounts.length) {
                selectAccount(accounts[accountId - 1].id);
            }
        }

        function validateForm() {
            const amountInput = document.getElementById('amountInput');
            const saveButton = document.getElementById('saveButton');
            
            const amount = parseFloat(amountInput.value);
            const isValid = amount > 0 && selectedCategoryId && selectedAccountId;
            
            saveButton.disabled = !isValid;
        }

        function saveIncomeRecord() {
            const amountInput = document.getElementById('amountInput');
            const noteInput = document.getElementById('noteInput');
            const dateInput = document.getElementById('dateInput');

            const transactionData = {
                amount: parseFloat(amountInput.value),
                type: 'income',
                categoryId: selectedCategoryId,
                accountId: selectedAccountId,
                note: noteInput.value || '',
                date: new Date(dateInput.value).toISOString()
            };

            try {
                if (editingTransactionId) {
                    // 编辑模式：更新现有记录
                    dataManager.updateTransaction(editingTransactionId, transactionData);
                    alert('收入记录已更新！');
                } else {
                    // 添加模式：创建新记录
                    dataManager.addTransaction(transactionData);
                    alert('收入记录已保存！');
                }

                // 返回详情页面或记账页面
                window.location.href = editingTransactionId ? 'details.html' : 'record.html';
            } catch (error) {
                alert(editingTransactionId ? '更新失败，请重试' : '保存失败，请重试');
                console.error('保存收入记录失败:', error);
            }
        }
    </script>
</body>
</html>
