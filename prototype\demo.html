<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - Demo</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title">财富管家</div>
                <div class="navbar-right">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 这里是页面具体内容 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">示例卡片</div>
                    </div>
                    <div class="card-content">
                        <p>这是一个示例页面，展示iOS风格的组件。</p>
                    </div>
                </div>

                <div class="list-group">
                    <a href="#" class="list-item">
                        <div class="list-icon" style="background: var(--ios-blue);">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">示例列表项</div>
                            <div class="list-subtitle">这是一个示例描述</div>
                        </div>
                        <div class="list-value">¥100.00</div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                    <a href="#" class="list-item">
                        <div class="list-icon" style="background: var(--ios-green);">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">另一个列表项</div>
                            <div class="list-subtitle">描述信息</div>
                        </div>
                        <div class="list-value">¥200.00</div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                </div>

                <div class="form-group">
                    <label class="form-label">示例输入框</label>
                    <input type="text" class="form-control" placeholder="请输入内容">
                </div>

                <button class="btn btn-primary btn-large">主要按钮</button>
            </div>

            <!-- 底部标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item active">
                    <div class="tab-icon"><i class="fas fa-home"></i></div>
                    <div>首页</div>
                </a>
                <a href="record.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-plus-circle"></i></div>
                    <div>记账</div>
                </a>
                <a href="statistics.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-chart-bar"></i></div>
                    <div>统计</div>
                </a>
                <a href="budget.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-wallet"></i></div>
                    <div>预算</div>
                </a>
                <a href="account.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-credit-card"></i></div>
                    <div>账户</div>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <!-- 浮动按钮 -->
    <button class="fab">
        <i class="fas fa-plus"></i>
    </button>

    <script>
        // 基础交互脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 标签栏切换
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 浮动按钮点击
            const fab = document.querySelector('.fab');
            if (fab) {
                fab.addEventListener('click', function() {
                    // 这里可以添加快速记账功能
                    alert('快速记账功能');
                });
            }

            // 列表项点击效果
            const listItems = document.querySelectorAll('.list-item');
            listItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.style.backgroundColor = 'var(--ios-gray6)';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
