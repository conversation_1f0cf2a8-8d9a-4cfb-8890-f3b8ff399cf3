<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 首页</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .balance-card {
            background: linear-gradient(135deg, var(--ios-blue), #5AC8FA);
            color: white;
            margin: 16px;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
        }
        
        .balance-amount {
            font-size: 36px;
            font-weight: 700;
            margin: 8px 0;
        }
        
        .balance-label {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .quick-stats {
            display: flex;
            margin: 0 16px 16px;
            gap: 8px;
        }
        
        .stat-item {
            flex: 1;
            background: var(--ios-white);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }
        
        .stat-amount {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 13px;
            color: var(--ios-secondary-label);
        }
        
        .income { color: var(--ios-green); }
        .expense { color: var(--ios-red); }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            margin-bottom: 0;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
        }
        
        .section-action {
            color: var(--ios-blue);
            font-size: 16px;
            text-decoration: none;
        }
        
        .transaction-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: var(--ios-white);
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
        }
        
        .transaction-info {
            flex: 1;
        }
        
        .transaction-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .transaction-detail {
            font-size: 13px;
            color: var(--ios-secondary-label);
        }
        
        .transaction-amount {
            font-size: 16px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-user-circle"></i>
                    </a>
                </div>
                <div class="navbar-title">财富管家</div>
                <div class="navbar-right">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-bell"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 余额卡片 -->
                <div class="balance-card">
                    <div class="balance-label">总资产</div>
                    <div class="balance-amount">¥12,580.50</div>
                    <div class="balance-label">截至今日</div>
                </div>

                <!-- 快速统计 -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <div class="stat-amount income">+¥3,200</div>
                        <div class="stat-label">本月收入</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-amount expense">-¥2,150</div>
                        <div class="stat-label">本月支出</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-amount text-primary">¥1,050</div>
                        <div class="stat-label">本月结余</div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="list-group">
                    <a href="quick-record.html" class="list-item">
                        <div class="list-icon" style="background: var(--ios-blue);">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">快速记账</div>
                            <div class="list-subtitle">记录收入或支出</div>
                        </div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                    <a href="details.html" class="list-item">
                        <div class="list-icon" style="background: var(--ios-green);">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">收支详情</div>
                            <div class="list-subtitle">查看详细记录</div>
                        </div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                </div>

                <!-- 最近交易 -->
                <div class="section-header">
                    <div class="section-title">最近交易</div>
                    <a href="details.html" class="section-action">查看全部</a>
                </div>

                <div style="background: var(--ios-white); margin: 0 16px; border-radius: 12px; overflow: hidden;">
                    <div class="transaction-item">
                        <div class="transaction-icon" style="background: #FF6B6B;">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="transaction-info">
                            <div class="transaction-title">午餐</div>
                            <div class="transaction-detail">今天 12:30 · 现金</div>
                        </div>
                        <div class="transaction-amount expense">-¥35.00</div>
                    </div>
                    
                    <div class="transaction-item">
                        <div class="transaction-icon" style="background: #4ECDC4;">
                            <i class="fas fa-subway"></i>
                        </div>
                        <div class="transaction-info">
                            <div class="transaction-title">地铁</div>
                            <div class="transaction-detail">今天 09:15 · 交通卡</div>
                        </div>
                        <div class="transaction-amount expense">-¥6.00</div>
                    </div>
                    
                    <div class="transaction-item">
                        <div class="transaction-icon" style="background: #45B7D1;">
                            <i class="fas fa-coffee"></i>
                        </div>
                        <div class="transaction-info">
                            <div class="transaction-title">咖啡</div>
                            <div class="transaction-detail">昨天 15:20 · 支付宝</div>
                        </div>
                        <div class="transaction-amount expense">-¥28.00</div>
                    </div>
                    
                    <div class="transaction-item" style="border-bottom: none;">
                        <div class="transaction-icon" style="background: var(--ios-green);">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="transaction-info">
                            <div class="transaction-title">工资</div>
                            <div class="transaction-detail">昨天 10:00 · 银行卡</div>
                        </div>
                        <div class="transaction-amount income">+¥8,500.00</div>
                    </div>
                </div>
            </div>

            <!-- 底部标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item active">
                    <div class="tab-icon"><i class="fas fa-home"></i></div>
                    <div>首页</div>
                </a>
                <a href="record.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-plus-circle"></i></div>
                    <div>记账</div>
                </a>
                <a href="statistics.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-chart-bar"></i></div>
                    <div>统计</div>
                </a>
                <a href="budget.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-wallet"></i></div>
                    <div>预算</div>
                </a>
                <a href="account.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-credit-card"></i></div>
                    <div>账户</div>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <!-- 浮动按钮 -->
    <button class="fab" onclick="location.href='quick-record.html'">
        <i class="fas fa-plus"></i>
    </button>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签栏切换
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const href = this.getAttribute('href');
                    if (href && href !== '#') {
                        window.location.href = href;
                    }
                });
            });

            // 交易项点击效果
            const transactionItems = document.querySelectorAll('.transaction-item');
            transactionItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.style.backgroundColor = 'var(--ios-gray6)';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
