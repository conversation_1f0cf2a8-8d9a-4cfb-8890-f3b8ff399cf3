<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 添加分类</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .form-section {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 16px;
            overflow: hidden;
        }
        
        .form-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .form-item:last-child {
            border-bottom: none;
        }
        
        .form-label {
            width: 80px;
            font-size: 16px;
            color: var(--ios-label);
        }
        
        .form-input {
            flex: 1;
            border: none;
            font-size: 16px;
            color: var(--ios-label);
            background: transparent;
            text-align: right;
        }
        
        .form-input:focus {
            outline: none;
        }
        
        .type-selector {
            display: flex;
            gap: 8px;
        }
        
        .type-option {
            flex: 1;
            padding: 8px 16px;
            border-radius: 8px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid var(--ios-separator);
        }
        
        .type-option.selected {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 12px;
            padding: 16px;
        }
        
        .icon-item {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
            color: white;
            font-size: 16px;
        }
        
        .icon-item.selected {
            border-color: var(--ios-blue);
            transform: scale(1.1);
        }
        
        .color-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 12px;
            padding: 16px;
        }
        
        .color-item {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }
        
        .color-item.selected {
            border-color: var(--ios-label);
            transform: scale(1.1);
        }
        
        .preview-section {
            text-align: center;
            padding: 20px;
        }
        
        .preview-icon {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 24px;
            color: white;
            background: var(--ios-gray);
        }
        
        .preview-name {
            font-size: 16px;
            font-weight: 500;
            color: var(--ios-label);
        }
        
        .save-button {
            margin: 16px;
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: calc(100% - 32px);
        }
        
        .save-button:disabled {
            background: var(--ios-gray5);
            color: var(--ios-gray);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="category-manage.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title">添加分类</div>
                <div class="navbar-right">
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 预览 -->
                <div class="form-section">
                    <div class="preview-section">
                        <div class="preview-icon" id="previewIcon">
                            <i class="fas fa-question"></i>
                        </div>
                        <div class="preview-name" id="previewName">新分类</div>
                    </div>
                </div>

                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">名称</div>
                        <input type="text" class="form-input" id="nameInput" placeholder="输入分类名称" maxlength="10">
                    </div>
                    <div class="form-item">
                        <div class="form-label">类型</div>
                        <div class="type-selector">
                            <div class="type-option selected" data-type="expense">支出</div>
                            <div class="type-option" data-type="income">收入</div>
                        </div>
                    </div>
                    <div class="form-item" id="budgetItem">
                        <div class="form-label">预算</div>
                        <input type="number" class="form-input" id="budgetInput" placeholder="月度预算(可选)" step="0.01">
                    </div>
                </div>

                <!-- 图标选择 -->
                <div class="form-section">
                    <div class="form-item" style="border-bottom: none; padding-bottom: 8px;">
                        <div class="form-label">图标</div>
                    </div>
                    <div class="icon-grid" id="iconGrid">
                        <!-- 图标将通过JavaScript动态加载 -->
                    </div>
                </div>

                <!-- 颜色选择 -->
                <div class="form-section">
                    <div class="form-item" style="border-bottom: none; padding-bottom: 8px;">
                        <div class="form-label">颜色</div>
                    </div>
                    <div class="color-grid" id="colorGrid">
                        <!-- 颜色将通过JavaScript动态加载 -->
                    </div>
                </div>

                <!-- 保存按钮 -->
                <button class="save-button" id="saveButton" disabled>保存分类</button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="data-manager.js"></script>
    <script>
        let selectedType = 'expense';
        let selectedIcon = null;
        let selectedColor = null;

        const icons = [
            'fas fa-utensils', 'fas fa-subway', 'fas fa-shopping-bag', 'fas fa-gamepad',
            'fas fa-heartbeat', 'fas fa-graduation-cap', 'fas fa-home', 'fas fa-car',
            'fas fa-coffee', 'fas fa-tshirt', 'fas fa-book', 'fas fa-dumbbell',
            'fas fa-plane', 'fas fa-gift', 'fas fa-phone', 'fas fa-laptop',
            'fas fa-music', 'fas fa-camera', 'fas fa-bicycle', 'fas fa-paw',
            'fas fa-money-bill-wave', 'fas fa-chart-line', 'fas fa-briefcase', 'fas fa-piggy-bank'
        ];

        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
            '#FFEAA7', '#DDA0DD', '#98D8C8', '#F39C12',
            '#E74C3C', '#3498DB', '#2ECC71', '#9B59B6',
            '#F1C40F', '#E67E22', '#1ABC9C', '#34495E'
        ];

        document.addEventListener('DOMContentLoaded', function() {
            loadIcons();
            loadColors();
            bindEvents();
            updatePreview();
        });

        function loadIcons() {
            const iconGrid = document.getElementById('iconGrid');
            iconGrid.innerHTML = '';
            
            icons.forEach(icon => {
                const iconElement = document.createElement('div');
                iconElement.className = 'icon-item';
                iconElement.style.background = selectedColor || '#8E8E93';
                iconElement.innerHTML = `<i class="${icon}"></i>`;
                iconElement.addEventListener('click', function() {
                    selectIcon(icon);
                });
                iconGrid.appendChild(iconElement);
            });
        }

        function loadColors() {
            const colorGrid = document.getElementById('colorGrid');
            colorGrid.innerHTML = '';
            
            colors.forEach(color => {
                const colorElement = document.createElement('div');
                colorElement.className = 'color-item';
                colorElement.style.background = color;
                colorElement.addEventListener('click', function() {
                    selectColor(color);
                });
                colorGrid.appendChild(colorElement);
            });
        }

        function selectIcon(icon) {
            document.querySelectorAll('.icon-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            event.target.closest('.icon-item').classList.add('selected');
            selectedIcon = icon;
            updatePreview();
            validateForm();
        }

        function selectColor(color) {
            document.querySelectorAll('.color-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            event.target.classList.add('selected');
            selectedColor = color;
            
            // 更新图标颜色
            document.querySelectorAll('.icon-item').forEach(item => {
                item.style.background = color;
            });
            
            updatePreview();
            validateForm();
        }

        function bindEvents() {
            const nameInput = document.getElementById('nameInput');
            const typeOptions = document.querySelectorAll('.type-option');
            const saveButton = document.getElementById('saveButton');

            nameInput.addEventListener('input', function() {
                updatePreview();
                validateForm();
            });

            typeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    typeOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedType = this.getAttribute('data-type');
                    
                    // 显示/隐藏预算输入
                    const budgetItem = document.getElementById('budgetItem');
                    budgetItem.style.display = selectedType === 'expense' ? 'flex' : 'none';
                    
                    validateForm();
                });
            });

            saveButton.addEventListener('click', function() {
                saveCategory();
            });
        }

        function updatePreview() {
            const nameInput = document.getElementById('nameInput');
            const previewIcon = document.getElementById('previewIcon');
            const previewName = document.getElementById('previewName');

            previewName.textContent = nameInput.value || '新分类';
            
            if (selectedIcon) {
                previewIcon.innerHTML = `<i class="${selectedIcon}"></i>`;
            }
            
            if (selectedColor) {
                previewIcon.style.background = selectedColor;
            }
        }

        function validateForm() {
            const nameInput = document.getElementById('nameInput');
            const saveButton = document.getElementById('saveButton');
            
            const isValid = nameInput.value.trim() && selectedIcon && selectedColor;
            saveButton.disabled = !isValid;
        }

        function saveCategory() {
            const nameInput = document.getElementById('nameInput');
            const budgetInput = document.getElementById('budgetInput');

            const categoryData = {
                name: nameInput.value.trim(),
                type: selectedType,
                icon: selectedIcon,
                color: selectedColor,
                budget: selectedType === 'expense' ? (parseFloat(budgetInput.value) || 0) : 0
            };

            try {
                dataManager.addCategory(categoryData);
                alert('分类已保存！');
                window.location.href = 'category-manage.html';
            } catch (error) {
                alert('保存失败，请重试');
                console.error('保存分类失败:', error);
            }
        }
    </script>
</body>
</html>
