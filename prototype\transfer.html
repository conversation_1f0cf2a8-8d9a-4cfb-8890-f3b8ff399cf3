<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 转账记录</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .form-section {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 16px;
            overflow: hidden;
        }
        
        .form-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .form-item:last-child {
            border-bottom: none;
        }
        
        .form-label {
            width: 80px;
            font-size: 16px;
            color: var(--ios-label);
        }
        
        .form-input {
            flex: 1;
            border: none;
            font-size: 16px;
            color: var(--ios-label);
            background: transparent;
            text-align: right;
        }
        
        .form-input:focus {
            outline: none;
        }
        
        .amount-input {
            font-size: 24px;
            font-weight: 600;
            color: var(--ios-blue);
        }
        
        .account-selector {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            cursor: pointer;
        }
        
        .selected-account {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .account-icon {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }
        
        .transfer-arrow {
            text-align: center;
            padding: 16px;
            color: var(--ios-blue);
            font-size: 20px;
        }
        
        .save-button {
            margin: 16px;
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: calc(100% - 32px);
        }
        
        .save-button:disabled {
            background: var(--ios-gray5);
            color: var(--ios-gray);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="record.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title">转账记录</div>
                <div class="navbar-right">
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 金额输入 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">金额</div>
                        <input type="number" class="form-input amount-input" id="amountInput" placeholder="0.00" step="0.01">
                    </div>
                </div>

                <!-- 转出账户 -->
                <div class="form-section">
                    <div class="form-item" id="fromAccountSelector">
                        <div class="form-label">转出</div>
                        <div class="account-selector">
                            <div class="selected-account" id="selectedFromAccount">
                                <span>请选择转出账户</span>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--ios-gray2);"></i>
                        </div>
                    </div>
                </div>

                <!-- 转账箭头 -->
                <div class="transfer-arrow">
                    <i class="fas fa-arrow-down"></i>
                </div>

                <!-- 转入账户 -->
                <div class="form-section">
                    <div class="form-item" id="toAccountSelector">
                        <div class="form-label">转入</div>
                        <div class="account-selector">
                            <div class="selected-account" id="selectedToAccount">
                                <span>请选择转入账户</span>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--ios-gray2);"></i>
                        </div>
                    </div>
                </div>

                <!-- 备注 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">备注</div>
                        <input type="text" class="form-input" id="noteInput" placeholder="添加备注">
                    </div>
                </div>

                <!-- 日期时间 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">日期</div>
                        <input type="datetime-local" class="form-input" id="dateInput">
                    </div>
                </div>

                <!-- 保存按钮 -->
                <button class="save-button" id="saveButton" disabled>确认转账</button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="data-manager.js"></script>
    <script>
        let selectedFromAccountId = null;
        let selectedToAccountId = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializeDateTime();
            bindEvents();
        });

        function initializeDateTime() {
            const dateInput = document.getElementById('dateInput');
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
                .toISOString().slice(0, 16);
            dateInput.value = localDateTime;
        }

        function bindEvents() {
            const amountInput = document.getElementById('amountInput');
            const fromAccountSelector = document.getElementById('fromAccountSelector');
            const toAccountSelector = document.getElementById('toAccountSelector');
            const saveButton = document.getElementById('saveButton');

            amountInput.addEventListener('input', validateForm);

            fromAccountSelector.addEventListener('click', function() {
                showAccountSelector('from');
            });

            toAccountSelector.addEventListener('click', function() {
                showAccountSelector('to');
            });

            saveButton.addEventListener('click', function() {
                saveTransfer();
            });
        }

        function showAccountSelector(type) {
            const accounts = dataManager.getAccounts();
            const excludeId = type === 'from' ? selectedToAccountId : selectedFromAccountId;
            
            const availableAccounts = accounts.filter(acc => acc.id !== excludeId);
            
            if (availableAccounts.length === 0) {
                alert('没有可用的账户');
                return;
            }

            const accountId = prompt(`选择${type === 'from' ? '转出' : '转入'}账户:\n` + 
                availableAccounts.map((acc, index) => 
                    `${index + 1}. ${acc.name} (¥${acc.balance.toFixed(2)})`
                ).join('\n') + '\n\n请输入序号:');

            if (accountId && accountId >= 1 && accountId <= availableAccounts.length) {
                const selectedAccount = availableAccounts[accountId - 1];
                
                if (type === 'from') {
                    selectFromAccount(selectedAccount.id);
                } else {
                    selectToAccount(selectedAccount.id);
                }
            }
        }

        function selectFromAccount(accountId) {
            const account = dataManager.getAccount(accountId);
            if (account) {
                selectedFromAccountId = accountId;
                const selectedElement = document.getElementById('selectedFromAccount');
                selectedElement.innerHTML = `
                    <div class="account-icon" style="background: ${account.color};">
                        <i class="${account.icon}"></i>
                    </div>
                    <span>${account.name}</span>
                `;
                validateForm();
            }
        }

        function selectToAccount(accountId) {
            const account = dataManager.getAccount(accountId);
            if (account) {
                selectedToAccountId = accountId;
                const selectedElement = document.getElementById('selectedToAccount');
                selectedElement.innerHTML = `
                    <div class="account-icon" style="background: ${account.color};">
                        <i class="${account.icon}"></i>
                    </div>
                    <span>${account.name}</span>
                `;
                validateForm();
            }
        }

        function validateForm() {
            const amountInput = document.getElementById('amountInput');
            const saveButton = document.getElementById('saveButton');
            
            const amount = parseFloat(amountInput.value);
            const isValid = amount > 0 && 
                           selectedFromAccountId && 
                           selectedToAccountId && 
                           selectedFromAccountId !== selectedToAccountId;
            
            saveButton.disabled = !isValid;
        }

        function saveTransfer() {
            const amountInput = document.getElementById('amountInput');
            const noteInput = document.getElementById('noteInput');
            const dateInput = document.getElementById('dateInput');

            const amount = parseFloat(amountInput.value);
            const fromAccount = dataManager.getAccount(selectedFromAccountId);
            
            // 检查余额是否足够
            if (fromAccount.balance < amount) {
                alert('转出账户余额不足');
                return;
            }

            try {
                // 更新账户余额
                dataManager.updateAccount(selectedFromAccountId, { 
                    balance: fromAccount.balance - amount 
                });
                
                const toAccount = dataManager.getAccount(selectedToAccountId);
                dataManager.updateAccount(selectedToAccountId, { 
                    balance: toAccount.balance + amount 
                });

                // 记录转账交易（可以选择是否记录为两笔交易）
                const transferNote = noteInput.value || '账户转账';
                const transferDate = new Date(dateInput.value).toISOString();

                // 显示成功消息
                alert('转账成功！');
                
                // 返回记账页面
                window.location.href = 'record.html';
            } catch (error) {
                alert('转账失败，请重试');
                console.error('转账失败:', error);
            }
        }
    </script>
</body>
</html>
