<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 添加支出</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .form-section {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 16px;
            overflow: hidden;
        }
        
        .form-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .form-item:last-child {
            border-bottom: none;
        }
        
        .form-label {
            width: 80px;
            font-size: 16px;
            color: var(--ios-label);
        }
        
        .form-input {
            flex: 1;
            border: none;
            font-size: 16px;
            color: var(--ios-label);
            background: transparent;
            text-align: right;
        }
        
        .form-input:focus {
            outline: none;
        }
        
        .form-input::placeholder {
            color: var(--ios-tertiary-label);
        }
        
        .amount-input {
            font-size: 24px;
            font-weight: 600;
            color: var(--ios-red);
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            padding: 20px;
        }
        
        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 8px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }
        
        .category-item:hover {
            background: var(--ios-gray6);
        }
        
        .category-item.selected {
            border-color: var(--ios-red);
            background: rgba(255, 59, 48, 0.1);
        }
        
        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: 8px;
            color: white;
        }
        
        .category-name {
            font-size: 12px;
            text-align: center;
            color: var(--ios-label);
        }
        
        .account-selector {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }
        
        .selected-account {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .account-icon {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }
        
        .save-button {
            margin: 16px;
            background: var(--ios-red);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: calc(100% - 32px);
        }
        
        .save-button:disabled {
            background: var(--ios-gray5);
            color: var(--ios-gray);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="record.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title">添加支出</div>
                <div class="navbar-right">
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 金额输入 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">金额</div>
                        <input type="number" class="form-input amount-input" id="amountInput" placeholder="0.00" step="0.01">
                    </div>
                </div>

                <!-- 分类选择 -->
                <div class="form-section">
                    <div class="form-item" style="border-bottom: none; padding-bottom: 8px;">
                        <div class="form-label">分类</div>
                    </div>
                    <div class="category-grid" id="categoryGrid">
                        <!-- 分类将通过JavaScript动态加载 -->
                    </div>
                </div>

                <!-- 账户选择 -->
                <div class="form-section">
                    <div class="form-item" id="accountSelector">
                        <div class="form-label">账户</div>
                        <div class="account-selector">
                            <div class="selected-account" id="selectedAccount">
                                <span>请选择账户</span>
                            </div>
                            <i class="fas fa-chevron-right" style="color: var(--ios-gray2);"></i>
                        </div>
                    </div>
                </div>

                <!-- 备注 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">备注</div>
                        <input type="text" class="form-input" id="noteInput" placeholder="添加备注">
                    </div>
                </div>

                <!-- 日期时间 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">日期</div>
                        <input type="datetime-local" class="form-input" id="dateInput">
                    </div>
                </div>

                <!-- 保存按钮 -->
                <button class="save-button" id="saveButton" disabled>保存支出记录</button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="data-manager.js"></script>
    <script>
        let selectedCategoryId = null;
        let selectedAccountId = null;

        document.addEventListener('DOMContentLoaded', function() {
            loadExpenseCategories();
            loadAccounts();
            initializeDateTime();
            bindEvents();
        });

        function loadExpenseCategories() {
            const categoryGrid = document.getElementById('categoryGrid');
            const expenseCategories = dataManager.getCategories('expense');
            
            categoryGrid.innerHTML = '';
            expenseCategories.forEach(category => {
                const categoryElement = document.createElement('div');
                categoryElement.className = 'category-item';
                categoryElement.dataset.categoryId = category.id;
                categoryElement.innerHTML = `
                    <div class="category-icon" style="background: ${category.color};">
                        <i class="${category.icon}"></i>
                    </div>
                    <div class="category-name">${category.name}</div>
                `;
                
                categoryElement.addEventListener('click', function() {
                    selectCategory(category.id);
                });
                
                categoryGrid.appendChild(categoryElement);
            });
        }

        function loadAccounts() {
            const accounts = dataManager.getAccounts();
            if (accounts.length > 0) {
                // 默认选择第一个账户
                selectAccount(accounts[0].id);
            }
        }

        function selectCategory(categoryId) {
            // 清除之前的选择
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 选择新分类
            const categoryElement = document.querySelector(`[data-category-id="${categoryId}"]`);
            if (categoryElement) {
                categoryElement.classList.add('selected');
                selectedCategoryId = categoryId;
                validateForm();
            }
        }

        function selectAccount(accountId) {
            const account = dataManager.getAccount(accountId);
            if (account) {
                selectedAccountId = accountId;
                const selectedAccountElement = document.getElementById('selectedAccount');
                selectedAccountElement.innerHTML = `
                    <div class="account-icon" style="background: ${account.color};">
                        <i class="${account.icon}"></i>
                    </div>
                    <span>${account.name}</span>
                `;
                validateForm();
            }
        }

        function initializeDateTime() {
            const dateInput = document.getElementById('dateInput');
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
                .toISOString().slice(0, 16);
            dateInput.value = localDateTime;
        }

        function bindEvents() {
            const amountInput = document.getElementById('amountInput');
            const saveButton = document.getElementById('saveButton');
            const accountSelector = document.getElementById('accountSelector');

            amountInput.addEventListener('input', validateForm);

            accountSelector.addEventListener('click', function() {
                showAccountSelector();
            });

            saveButton.addEventListener('click', function() {
                saveExpenseRecord();
            });
        }

        function showAccountSelector() {
            const accounts = dataManager.getAccounts();
            const accountId = prompt('选择账户:\n' + accounts.map((acc, index) => 
                `${index + 1}. ${acc.name} (¥${acc.balance.toFixed(2)})`
            ).join('\n') + '\n\n请输入序号:');

            if (accountId && accountId >= 1 && accountId <= accounts.length) {
                selectAccount(accounts[accountId - 1].id);
            }
        }

        function validateForm() {
            const amountInput = document.getElementById('amountInput');
            const saveButton = document.getElementById('saveButton');
            
            const amount = parseFloat(amountInput.value);
            const isValid = amount > 0 && selectedCategoryId && selectedAccountId;
            
            saveButton.disabled = !isValid;
        }

        function saveExpenseRecord() {
            const amountInput = document.getElementById('amountInput');
            const noteInput = document.getElementById('noteInput');
            const dateInput = document.getElementById('dateInput');

            const transactionData = {
                amount: parseFloat(amountInput.value),
                type: 'expense',
                categoryId: selectedCategoryId,
                accountId: selectedAccountId,
                note: noteInput.value || '',
                date: new Date(dateInput.value).toISOString()
            };

            try {
                dataManager.addTransaction(transactionData);
                
                // 显示成功消息
                alert('支出记录已保存！');
                
                // 返回记账页面
                window.location.href = 'record.html';
            } catch (error) {
                alert('保存失败，请重试');
                console.error('保存支出记录失败:', error);
            }
        }
    </script>
</body>
</html>
