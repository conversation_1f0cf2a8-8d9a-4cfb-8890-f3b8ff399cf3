<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 快速记账</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .amount-input {
            text-align: center;
            padding: 40px 20px;
            background: var(--ios-white);
            margin: 16px;
            border-radius: 16px;
        }
        
        .amount-display {
            font-size: 48px;
            font-weight: 300;
            color: var(--ios-label);
            margin-bottom: 8px;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .amount-label {
            font-size: 16px;
            color: var(--ios-secondary-label);
        }
        
        .type-selector {
            display: flex;
            margin: 0 16px 16px;
            background: var(--ios-white);
            border-radius: 12px;
            padding: 4px;
        }
        
        .type-option {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .type-option.active {
            background: var(--ios-blue);
            color: white;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin: 0 16px 16px;
            padding: 16px;
            background: var(--ios-white);
            border-radius: 12px;
        }
        
        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 8px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .category-item:hover {
            background: var(--ios-gray6);
        }
        
        .category-item.selected {
            background: var(--ios-blue);
            color: white;
        }
        
        .category-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin-bottom: 6px;
            color: white;
        }
        
        .category-item.selected .category-icon {
            background: rgba(255, 255, 255, 0.2) !important;
        }
        
        .category-name {
            font-size: 12px;
            text-align: center;
        }
        
        .keypad {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1px;
            background: var(--ios-separator);
            margin: 0 16px;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .key {
            background: var(--ios-white);
            border: none;
            padding: 20px;
            font-size: 24px;
            font-weight: 400;
            cursor: pointer;
            transition: background 0.1s;
        }
        
        .key:hover {
            background: var(--ios-gray6);
        }
        
        .key:active {
            background: var(--ios-gray5);
        }
        
        .key.zero {
            grid-column: span 2;
        }
        
        .save-button {
            margin: 16px;
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
        }
        
        .save-button:disabled {
            background: var(--ios-gray5);
            color: var(--ios-gray);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="index.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title">快速记账</div>
                <div class="navbar-right">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-clock"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 金额输入 -->
                <div class="amount-input">
                    <div class="amount-display" id="amountDisplay">¥0</div>
                    <div class="amount-label">输入金额</div>
                </div>

                <!-- 类型选择 -->
                <div class="type-selector">
                    <div class="type-option active" data-type="expense">支出</div>
                    <div class="type-option" data-type="income">收入</div>
                </div>

                <!-- 分类选择 -->
                <div class="category-grid" id="categoryGrid">
                    <!-- 支出分类 -->
                    <div class="category-item" data-category="food">
                        <div class="category-icon" style="background: #FF6B6B;">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="category-name">餐饮</div>
                    </div>
                    <div class="category-item" data-category="transport">
                        <div class="category-icon" style="background: #4ECDC4;">
                            <i class="fas fa-subway"></i>
                        </div>
                        <div class="category-name">交通</div>
                    </div>
                    <div class="category-item" data-category="shopping">
                        <div class="category-icon" style="background: #45B7D1;">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div class="category-name">购物</div>
                    </div>
                    <div class="category-item" data-category="entertainment">
                        <div class="category-icon" style="background: #96CEB4;">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <div class="category-name">娱乐</div>
                    </div>
                    <div class="category-item" data-category="medical">
                        <div class="category-icon" style="background: #FFEAA7;">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <div class="category-name">医疗</div>
                    </div>
                    <div class="category-item" data-category="education">
                        <div class="category-icon" style="background: #DDA0DD;">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="category-name">教育</div>
                    </div>
                    <div class="category-item" data-category="housing">
                        <div class="category-icon" style="background: #98D8C8;">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="category-name">居住</div>
                    </div>
                    <div class="category-item" data-category="other">
                        <div class="category-icon" style="background: var(--ios-gray);">
                            <i class="fas fa-ellipsis-h"></i>
                        </div>
                        <div class="category-name">其他</div>
                    </div>
                </div>

                <!-- 数字键盘 -->
                <div class="keypad">
                    <button class="key" data-key="1">1</button>
                    <button class="key" data-key="2">2</button>
                    <button class="key" data-key="3">3</button>
                    <button class="key" data-key="4">4</button>
                    <button class="key" data-key="5">5</button>
                    <button class="key" data-key="6">6</button>
                    <button class="key" data-key="7">7</button>
                    <button class="key" data-key="8">8</button>
                    <button class="key" data-key="9">9</button>
                    <button class="key" data-key=".">.</button>
                    <button class="key zero" data-key="0">0</button>
                    <button class="key" data-key="delete">
                        <i class="fas fa-backspace"></i>
                    </button>
                </div>

                <!-- 保存按钮 -->
                <button class="save-button" id="saveButton" disabled>保存记录</button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        let currentAmount = '';
        let currentType = 'expense';
        let selectedCategory = null;

        const amountDisplay = document.getElementById('amountDisplay');
        const saveButton = document.getElementById('saveButton');
        const typeOptions = document.querySelectorAll('.type-option');
        const categoryItems = document.querySelectorAll('.category-item');
        const keys = document.querySelectorAll('.key');

        // 数字键盘事件
        keys.forEach(key => {
            key.addEventListener('click', function() {
                const keyValue = this.getAttribute('data-key');
                
                if (keyValue === 'delete') {
                    currentAmount = currentAmount.slice(0, -1);
                } else if (keyValue === '.' && !currentAmount.includes('.')) {
                    currentAmount += keyValue;
                } else if (keyValue !== '.') {
                    if (currentAmount === '0') {
                        currentAmount = keyValue;
                    } else {
                        currentAmount += keyValue;
                    }
                }
                
                updateAmountDisplay();
                updateSaveButton();
            });
        });

        // 类型切换
        typeOptions.forEach(option => {
            option.addEventListener('click', function() {
                typeOptions.forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
                currentType = this.getAttribute('data-type');
                updateCategories();
            });
        });

        // 分类选择
        categoryItems.forEach(item => {
            item.addEventListener('click', function() {
                categoryItems.forEach(cat => cat.classList.remove('selected'));
                this.classList.add('selected');
                selectedCategory = this.getAttribute('data-category');
                updateSaveButton();
            });
        });

        // 保存按钮
        saveButton.addEventListener('click', function() {
            if (currentAmount && selectedCategory) {
                // 这里可以添加保存逻辑
                alert(`记录已保存：${currentType === 'expense' ? '-' : '+'}¥${currentAmount} (${selectedCategory})`);
                // 返回首页
                window.location.href = 'index.html';
            }
        });

        function updateAmountDisplay() {
            const amount = currentAmount || '0';
            amountDisplay.textContent = `¥${amount}`;
        }

        function updateSaveButton() {
            const isValid = currentAmount && parseFloat(currentAmount) > 0 && selectedCategory;
            saveButton.disabled = !isValid;
        }

        function updateCategories() {
            // 这里可以根据收入/支出类型切换不同的分类
            if (currentType === 'income') {
                // 可以显示收入相关的分类
            }
        }

        // 初始化
        updateAmountDisplay();
        updateSaveButton();
    </script>
</body>
</html>
