<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 添加账户</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .form-section {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 16px;
            overflow: hidden;
        }
        
        .form-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .form-item:last-child {
            border-bottom: none;
        }
        
        .form-label {
            width: 80px;
            font-size: 16px;
            color: var(--ios-label);
        }
        
        .form-input {
            flex: 1;
            border: none;
            font-size: 16px;
            color: var(--ios-label);
            background: transparent;
            text-align: right;
        }
        
        .form-input:focus {
            outline: none;
        }
        
        .type-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            padding: 16px;
        }
        
        .type-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid var(--ios-separator);
        }
        
        .type-item.selected {
            border-color: var(--ios-blue);
            background: rgba(0, 122, 255, 0.1);
        }
        
        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: 8px;
            color: white;
        }
        
        .type-name {
            font-size: 14px;
            text-align: center;
            color: var(--ios-label);
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 12px;
            padding: 16px;
        }
        
        .icon-item {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
            color: white;
            font-size: 16px;
        }
        
        .icon-item.selected {
            border-color: var(--ios-blue);
            transform: scale(1.1);
        }
        
        .color-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 12px;
            padding: 16px;
        }
        
        .color-item {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }
        
        .color-item.selected {
            border-color: var(--ios-label);
            transform: scale(1.1);
        }
        
        .preview-section {
            text-align: center;
            padding: 20px;
        }
        
        .preview-icon {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 24px;
            color: white;
            background: var(--ios-gray);
        }
        
        .preview-name {
            font-size: 16px;
            font-weight: 500;
            color: var(--ios-label);
        }
        
        .save-button {
            margin: 16px;
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: calc(100% - 32px);
        }
        
        .save-button:disabled {
            background: var(--ios-gray5);
            color: var(--ios-gray);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="account-manage.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title" id="pageTitle">添加账户</div>
                <div class="navbar-right">
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 预览 -->
                <div class="form-section">
                    <div class="preview-section">
                        <div class="preview-icon" id="previewIcon">
                            <i class="fas fa-question"></i>
                        </div>
                        <div class="preview-name" id="previewName">新账户</div>
                    </div>
                </div>

                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="form-item">
                        <div class="form-label">名称</div>
                        <input type="text" class="form-input" id="nameInput" placeholder="输入账户名称" maxlength="20">
                    </div>
                    <div class="form-item">
                        <div class="form-label">初始余额</div>
                        <input type="number" class="form-input" id="balanceInput" placeholder="0.00" step="0.01">
                    </div>
                    <div class="form-item" id="cardNumberItem" style="display: none;">
                        <div class="form-label">卡号</div>
                        <input type="text" class="form-input" id="cardNumberInput" placeholder="****1234" maxlength="20">
                    </div>
                </div>

                <!-- 账户类型 -->
                <div class="form-section">
                    <div class="form-item" style="border-bottom: none; padding-bottom: 8px;">
                        <div class="form-label">类型</div>
                    </div>
                    <div class="type-grid" id="typeGrid">
                        <div class="type-item" data-type="bank_card">
                            <div class="type-icon" style="background: #3498DB;">
                                <i class="fas fa-university"></i>
                            </div>
                            <div class="type-name">储蓄卡</div>
                        </div>
                        <div class="type-item" data-type="credit_card">
                            <div class="type-icon" style="background: #E74C3C;">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="type-name">信用卡</div>
                        </div>
                        <div class="type-item" data-type="e_wallet">
                            <div class="type-icon" style="background: #2ECC71;">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="type-name">电子钱包</div>
                        </div>
                        <div class="type-item" data-type="cash">
                            <div class="type-icon" style="background: #F39C12;">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <div class="type-name">现金</div>
                        </div>
                    </div>
                </div>

                <!-- 图标选择 -->
                <div class="form-section">
                    <div class="form-item" style="border-bottom: none; padding-bottom: 8px;">
                        <div class="form-label">图标</div>
                    </div>
                    <div class="icon-grid" id="iconGrid">
                        <!-- 图标将通过JavaScript动态加载 -->
                    </div>
                </div>

                <!-- 颜色选择 -->
                <div class="form-section">
                    <div class="form-item" style="border-bottom: none; padding-bottom: 8px;">
                        <div class="form-label">颜色</div>
                    </div>
                    <div class="color-grid" id="colorGrid">
                        <!-- 颜色将通过JavaScript动态加载 -->
                    </div>
                </div>

                <!-- 保存按钮 -->
                <button class="save-button" id="saveButton" disabled>保存账户</button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="data-manager.js"></script>
    <script>
        let selectedType = null;
        let selectedIcon = null;
        let selectedColor = null;
        let editingAccountId = null;

        const icons = [
            'fas fa-university', 'fas fa-credit-card', 'fas fa-mobile-alt', 'fas fa-wallet',
            'fas fa-piggy-bank', 'fas fa-coins', 'fas fa-money-bill-wave', 'fas fa-landmark',
            'fas fa-building', 'fas fa-home', 'fas fa-car', 'fas fa-plane'
        ];

        const colors = [
            '#3498DB', '#E74C3C', '#2ECC71', '#F39C12',
            '#9B59B6', '#1ABC9C', '#34495E', '#E67E22',
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'
        ];

        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否是编辑模式
            const urlParams = new URLSearchParams(window.location.search);
            editingAccountId = urlParams.get('edit');

            if (editingAccountId) {
                document.getElementById('pageTitle').textContent = '编辑账户';
                loadAccountForEdit(editingAccountId);
            }

            loadIcons();
            loadColors();
            bindEvents();
            updatePreview();
        });

        function loadIcons() {
            const iconGrid = document.getElementById('iconGrid');
            iconGrid.innerHTML = '';
            
            icons.forEach(icon => {
                const iconElement = document.createElement('div');
                iconElement.className = 'icon-item';
                iconElement.style.background = selectedColor || '#8E8E93';
                iconElement.innerHTML = `<i class="${icon}"></i>`;
                iconElement.addEventListener('click', function() {
                    selectIcon(icon);
                });
                iconGrid.appendChild(iconElement);
            });
        }

        function loadColors() {
            const colorGrid = document.getElementById('colorGrid');
            colorGrid.innerHTML = '';
            
            colors.forEach(color => {
                const colorElement = document.createElement('div');
                colorElement.className = 'color-item';
                colorElement.style.background = color;
                colorElement.addEventListener('click', function() {
                    selectColor(color);
                });
                colorGrid.appendChild(colorElement);
            });
        }

        function selectType(type) {
            document.querySelectorAll('.type-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            event.target.closest('.type-item').classList.add('selected');
            selectedType = type;
            
            // 显示/隐藏卡号输入
            const cardNumberItem = document.getElementById('cardNumberItem');
            cardNumberItem.style.display = (type === 'bank_card' || type === 'credit_card') ? 'flex' : 'none';
            
            updatePreview();
            validateForm();
        }

        function selectIcon(icon) {
            document.querySelectorAll('.icon-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            event.target.closest('.icon-item').classList.add('selected');
            selectedIcon = icon;
            updatePreview();
            validateForm();
        }

        function selectColor(color) {
            document.querySelectorAll('.color-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            event.target.classList.add('selected');
            selectedColor = color;
            
            // 更新图标颜色
            document.querySelectorAll('.icon-item').forEach(item => {
                item.style.background = color;
            });
            
            updatePreview();
            validateForm();
        }

        function bindEvents() {
            const nameInput = document.getElementById('nameInput');
            const balanceInput = document.getElementById('balanceInput');
            const typeItems = document.querySelectorAll('.type-item');
            const saveButton = document.getElementById('saveButton');

            nameInput.addEventListener('input', function() {
                updatePreview();
                validateForm();
            });

            balanceInput.addEventListener('input', validateForm);

            typeItems.forEach(item => {
                item.addEventListener('click', function() {
                    selectType(this.getAttribute('data-type'));
                });
            });

            saveButton.addEventListener('click', function() {
                saveAccount();
            });
        }

        function updatePreview() {
            const nameInput = document.getElementById('nameInput');
            const previewIcon = document.getElementById('previewIcon');
            const previewName = document.getElementById('previewName');

            previewName.textContent = nameInput.value || '新账户';
            
            if (selectedIcon) {
                previewIcon.innerHTML = `<i class="${selectedIcon}"></i>`;
            }
            
            if (selectedColor) {
                previewIcon.style.background = selectedColor;
            }
        }

        function validateForm() {
            const nameInput = document.getElementById('nameInput');
            const balanceInput = document.getElementById('balanceInput');
            const saveButton = document.getElementById('saveButton');
            
            const isValid = nameInput.value.trim() && 
                           selectedType && 
                           selectedIcon && 
                           selectedColor &&
                           balanceInput.value !== '';
            
            saveButton.disabled = !isValid;
        }

        function loadAccountForEdit(accountId) {
            const account = dataManager.getAccount(accountId);
            if (!account) {
                alert('账户不存在');
                window.location.href = 'account-manage.html';
                return;
            }

            // 填充表单数据
            document.getElementById('nameInput').value = account.name;
            document.getElementById('balanceInput').value = account.balance;
            document.getElementById('cardNumberInput').value = account.cardNumber || '';

            // 设置类型
            selectedType = account.type;
            const typeOptions = document.querySelectorAll('.type-option');
            typeOptions.forEach(option => {
                option.classList.toggle('active', option.getAttribute('data-type') === selectedType);
            });

            // 设置图标和颜色
            selectedIcon = account.icon;
            selectedColor = account.color;

            // 更新保存按钮文本
            document.getElementById('saveButton').textContent = '更新账户';

            // 更新预览
            updatePreview();
        }

        function saveAccount() {
            const nameInput = document.getElementById('nameInput');
            const balanceInput = document.getElementById('balanceInput');
            const cardNumberInput = document.getElementById('cardNumberInput');

            const accountData = {
                name: nameInput.value.trim(),
                type: selectedType,
                balance: parseFloat(balanceInput.value) || 0,
                icon: selectedIcon,
                color: selectedColor,
                cardNumber: cardNumberInput.value.trim() || '',
                isDefault: false
            };

            try {
                if (editingAccountId) {
                    // 编辑模式：更新现有账户
                    dataManager.updateAccount(editingAccountId, accountData);
                    alert('账户已更新！');
                } else {
                    // 添加模式：创建新账户
                    dataManager.addAccount(accountData);
                    alert('账户已保存！');
                }
                window.location.href = 'account-manage.html';
            } catch (error) {
                alert(editingAccountId ? '更新失败，请重试' : '保存失败，请重试');
                console.error('保存账户失败:', error);
            }
        }
    </script>
</body>
</html>
