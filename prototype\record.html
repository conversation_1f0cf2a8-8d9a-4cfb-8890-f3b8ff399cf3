<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 记账</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .record-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin: 16px;
        }
        
        .record-option {
            background: var(--ios-white);
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            text-decoration: none;
            color: var(--ios-label);
            transition: transform 0.2s;
        }
        
        .record-option:hover {
            transform: scale(0.98);
        }
        
        .record-icon {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 24px;
            color: white;
        }
        
        .record-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .record-subtitle {
            font-size: 14px;
            color: var(--ios-secondary-label);
        }
        
        .income-option .record-icon {
            background: var(--ios-green);
        }
        
        .expense-option .record-icon {
            background: var(--ios-red);
        }
        
        .transfer-option .record-icon {
            background: var(--ios-blue);
        }
        
        .category-option .record-icon {
            background: var(--ios-purple);
        }
        
        .recent-section {
            margin: 24px 16px 16px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .recent-categories {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
        }
        
        .recent-category {
            min-width: 80px;
            background: var(--ios-white);
            border-radius: 12px;
            padding: 12px 8px;
            text-align: center;
            text-decoration: none;
            color: var(--ios-label);
        }
        
        .recent-category-icon {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 6px;
            font-size: 14px;
            color: white;
        }
        
        .recent-category-name {
            font-size: 12px;
        }
        
        .quick-amounts {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            margin: 16px;
        }
        
        .quick-amount {
            background: var(--ios-white);
            border: 1px solid var(--ios-separator);
            border-radius: 8px;
            padding: 12px 8px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .quick-amount:hover {
            background: var(--ios-gray6);
            border-color: var(--ios-blue);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-search"></i>
                    </a>
                </div>
                <div class="navbar-title">记账</div>
                <div class="navbar-right">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-history"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 记账选项 -->
                <div class="record-options">
                    <a href="add-income.html" class="record-option income-option">
                        <div class="record-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="record-title">添加收入</div>
                        <div class="record-subtitle">工资、奖金、投资收益</div>
                    </a>
                    
                    <a href="add-expense.html" class="record-option expense-option">
                        <div class="record-icon">
                            <i class="fas fa-minus"></i>
                        </div>
                        <div class="record-title">添加支出</div>
                        <div class="record-subtitle">日常消费、生活开支</div>
                    </a>
                    
                    <a href="transfer.html" class="record-option transfer-option">
                        <div class="record-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="record-title">转账记录</div>
                        <div class="record-subtitle">账户间资金转移</div>
                    </a>
                    
                    <a href="category-manage.html" class="record-option category-option">
                        <div class="record-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="record-title">分类管理</div>
                        <div class="record-subtitle">自定义收支分类</div>
                    </a>
                </div>

                <!-- 常用分类 -->
                <div class="recent-section">
                    <div class="section-title">常用分类</div>
                    <div class="recent-categories">
                        <a href="#" class="recent-category" data-category="food">
                            <div class="recent-category-icon" style="background: #FF6B6B;">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="recent-category-name">餐饮</div>
                        </a>
                        <a href="#" class="recent-category" data-category="transport">
                            <div class="recent-category-icon" style="background: #4ECDC4;">
                                <i class="fas fa-subway"></i>
                            </div>
                            <div class="recent-category-name">交通</div>
                        </a>
                        <a href="#" class="recent-category" data-category="shopping">
                            <div class="recent-category-icon" style="background: #45B7D1;">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="recent-category-name">购物</div>
                        </a>
                        <a href="#" class="recent-category" data-category="entertainment">
                            <div class="recent-category-icon" style="background: #96CEB4;">
                                <i class="fas fa-gamepad"></i>
                            </div>
                            <div class="recent-category-name">娱乐</div>
                        </a>
                        <a href="#" class="recent-category" data-category="coffee">
                            <div class="recent-category-icon" style="background: #D4A574;">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <div class="recent-category-name">咖啡</div>
                        </a>
                    </div>
                </div>

                <!-- 快速金额 -->
                <div class="recent-section">
                    <div class="section-title">快速金额</div>
                    <div class="quick-amounts">
                        <div class="quick-amount" data-amount="10">¥10</div>
                        <div class="quick-amount" data-amount="20">¥20</div>
                        <div class="quick-amount" data-amount="50">¥50</div>
                        <div class="quick-amount" data-amount="100">¥100</div>
                        <div class="quick-amount" data-amount="200">¥200</div>
                        <div class="quick-amount" data-amount="500">¥500</div>
                        <div class="quick-amount" data-amount="1000">¥1000</div>
                        <div class="quick-amount" data-amount="custom">自定义</div>
                    </div>
                </div>

                <!-- 今日记录摘要 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">今日记录</div>
                    </div>
                    <div class="card-content">
                        <div class="d-flex justify-between align-center mb-1">
                            <span>支出</span>
                            <span class="text-danger">-¥156.00</span>
                        </div>
                        <div class="d-flex justify-between align-center mb-1">
                            <span>收入</span>
                            <span class="text-success">+¥0.00</span>
                        </div>
                        <div class="d-flex justify-between align-center">
                            <span class="text-large">结余</span>
                            <span class="text-large text-danger">-¥156.00</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-home"></i></div>
                    <div>首页</div>
                </a>
                <a href="record.html" class="tab-item active">
                    <div class="tab-icon"><i class="fas fa-plus-circle"></i></div>
                    <div>记账</div>
                </a>
                <a href="statistics.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-chart-bar"></i></div>
                    <div>统计</div>
                </a>
                <a href="budget.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-wallet"></i></div>
                    <div>预算</div>
                </a>
                <a href="account.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-credit-card"></i></div>
                    <div>账户</div>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签栏切换
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const href = this.getAttribute('href');
                    if (href && href !== '#') {
                        window.location.href = href;
                    }
                });
            });

            // 常用分类点击
            const recentCategories = document.querySelectorAll('.recent-category');
            recentCategories.forEach(category => {
                category.addEventListener('click', function(e) {
                    e.preventDefault();
                    const categoryType = this.getAttribute('data-category');
                    // 跳转到快速记账页面并预选分类
                    window.location.href = `quick-record.html?category=${categoryType}`;
                });
            });

            // 快速金额点击
            const quickAmounts = document.querySelectorAll('.quick-amount');
            quickAmounts.forEach(amount => {
                amount.addEventListener('click', function() {
                    const amountValue = this.getAttribute('data-amount');
                    if (amountValue === 'custom') {
                        window.location.href = 'quick-record.html';
                    } else {
                        // 跳转到快速记账页面并预填金额
                        window.location.href = `quick-record.html?amount=${amountValue}`;
                    }
                });
            });

            // 记录选项点击效果
            const recordOptions = document.querySelectorAll('.record-option');
            recordOptions.forEach(option => {
                option.addEventListener('click', function(e) {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
