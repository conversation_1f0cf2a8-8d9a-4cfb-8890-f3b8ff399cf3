# 财富管家 iOS App 高保真原型

## 项目概述

这是基于需求文档设计的"财富管家"记账应用的高保真原型，采用iOS设计规范，模拟iPhone 15的显示效果。

## 文件结构

```
prototype/
├── ios-components.css      # iOS风格组件样式库
├── demo.html              # 基础模板页面
├── index.html             # 首页
├── quick-record.html      # 快速记账页面
├── record.html            # 记账主页面
├── statistics.html        # 统计页面
├── budget.html            # 预算页面
├── account.html           # 账户页面
├── details.html           # 收支详情页面
└── README.md              # 说明文档
```

## 页面功能说明

### 1. 首页 (index.html)
- **功能**: 展示总资产、本月收支概览、快速操作入口、最近交易记录
- **特色**: 渐变色资产卡片、统计数据卡片、交易记录列表
- **导航**: 可跳转到快速记账、收支详情等页面

### 2. 快速记账 (quick-record.html)
- **功能**: 快速记录收入或支出
- **特色**: 大字体金额显示、分类图标网格、数字键盘、类型切换
- **交互**: 支持数字输入、分类选择、保存记录

### 3. 记账主页 (record.html)
- **功能**: 记账功能入口、常用分类、快速金额、今日统计
- **特色**: 四宫格操作入口、横向滚动分类、快速金额网格
- **导航**: 跳转到添加收入、支出、转账、分类管理等页面

### 4. 统计页面 (statistics.html)
- **功能**: 收支统计、趋势分析、分类占比、详细报表
- **特色**: 时间选择器、饼图展示、趋势图表、分类统计列表
- **交互**: 支持时间段切换、数据筛选

### 5. 预算页面 (budget.html)
- **功能**: 预算管理、分类预算、储蓄目标、预算提醒
- **特色**: 渐变色预算卡片、进度条显示、预算状态指示
- **交互**: 预算进度动画、状态颜色区分

### 6. 账户页面 (account.html)
- **功能**: 账户管理、资产统计、账户列表
- **特色**: 总资产展示、账户图标、余额变化、添加账户入口
- **交互**: 账户详情跳转、添加新账户

### 7. 收支详情 (details.html)
- **功能**: 交易记录查看、搜索筛选、按日期分组
- **特色**: 搜索栏、筛选标签、日期分组、每日汇总
- **交互**: 搜索功能、筛选切换、交易详情查看

## 设计特色

### iOS设计规范
- **颜色**: 使用iOS系统色彩，包括蓝色、绿色、红色等
- **字体**: 采用苹方字体(PingFang SC)和系统字体
- **圆角**: 统一使用12px圆角半径
- **阴影**: 轻微阴影效果，符合iOS扁平化设计

### iPhone 15适配
- **尺寸**: 393×852px屏幕尺寸
- **状态栏**: 包含时间、信号、WiFi、电池等元素
- **Home Indicator**: 底部圆角指示器
- **安全区域**: 考虑刘海屏和圆角设计

### 交互体验
- **点击反馈**: 所有可点击元素都有视觉反馈
- **动画效果**: 进度条、按钮等有平滑动画
- **导航逻辑**: 清晰的页面跳转关系
- **浮动按钮**: 快速记账的FAB按钮

## 技术实现

### CSS特性
- **CSS Grid**: 用于网格布局
- **Flexbox**: 用于弹性布局
- **CSS变量**: 统一管理颜色和尺寸
- **渐变背景**: 卡片和按钮的渐变效果
- **响应式**: 支持不同屏幕尺寸

### JavaScript功能
- **标签栏切换**: 底部导航栏交互
- **表单交互**: 数字键盘、分类选择等
- **动画控制**: 进度条动画、点击效果
- **页面跳转**: 模拟真实的页面导航

## 使用方法

1. **本地预览**: 直接在浏览器中打开任意HTML文件
2. **推荐浏览器**: Chrome、Safari等现代浏览器
3. **最佳体验**: 使用浏览器的设备模拟器，选择iPhone尺寸
4. **起始页面**: 建议从index.html开始浏览

## 页面跳转关系

```
index.html (首页)
├── quick-record.html (快速记账)
├── details.html (收支详情)
└── 底部导航栏
    ├── record.html (记账)
    ├── statistics.html (统计)
    ├── budget.html (预算)
    └── account.html (账户)
```

## 注意事项

1. **图片资源**: 使用FontAwesome图标库，需要网络连接
2. **字体加载**: 优先使用系统字体，确保最佳显示效果
3. **交互限制**: 部分功能为演示效果，实际开发需要后端支持
4. **数据模拟**: 所有数据均为演示数据，非真实数据

## 后续开发建议

1. **数据持久化**: 集成本地存储或云端数据库
2. **用户认证**: 添加登录注册功能
3. **数据同步**: 支持多设备数据同步
4. **推送通知**: 预算提醒、记账提醒等

5. **更多图表**: 集成Chart.js等图表库
6. **手势操作**: 支持滑动删除、长按编辑等
7. **深色模式**: 支持iOS深色模式适配

---

**开发团队**: 财富管家产品团队  
**设计版本**: v1.0  
**更新日期**: 2024年1月
