# 财富管家 iOS App 高保真原型

## 项目概述

这是基于需求文档设计的"财富管家"记账应用的高保真原型，采用iOS设计规范，模拟iPhone 15的显示效果。

## 文件结构

```
prototype/
├── ios-components.css      # iOS风格组件样式库
├── data-manager.js         # 数据管理系统
├── demo.html              # 基础模板页面
├── index.html             # 首页
├── quick-record.html      # 快速记账页面
├── record.html            # 记账主页面
├── add-income.html        # 添加收入页面
├── add-expense.html       # 添加支出页面
├── transfer.html          # 转账记录页面
├── category-manage.html   # 分类管理页面
├── add-category.html      # 添加分类页面
├── statistics.html        # 统计页面
├── budget.html            # 预算页面
├── account.html           # 账户页面
├── account-manage.html    # 账户管理页面
├── add-account.html       # 添加账户页面
├── details.html           # 收支详情页面
└── README.md              # 说明文档
```

## 核心功能

### 🎯 数据管理系统 (data-manager.js)
- **本地存储**: 使用localStorage持久化数据
- **实时同步**: 数据变更自动通知所有页面更新
- **数据模型**: 完整的账户、分类、交易、预算数据结构
- **API接口**: 提供增删改查等完整数据操作方法

## 页面功能说明

### 1. 首页 (index.html) ✅ 数据集成
- **功能**: 实时显示总资产、本月收支概览、最近交易记录
- **特色**: 渐变色资产卡片、动态统计数据、真实交易列表
- **数据**: 自动计算总资产、收支统计、加载最新4笔交易

### 2. 快速记账 (quick-record.html) ✅ 数据集成
- **功能**: 快速记录收入或支出，自动更新账户余额
- **特色**: 动态分类加载、数字键盘、类型切换
- **数据**: 保存交易记录、更新账户余额、实时验证

### 3. 记账主页 (record.html) ✅ 数据集成
- **功能**: 记账功能入口、动态常用分类、今日统计
- **特色**: 四宫格操作入口、实时分类加载、今日收支汇总
- **导航**: 跳转到添加收入、支出、转账、分类管理等页面

### 4. 添加收入/支出 (add-income.html, add-expense.html) ✅ 完整功能
- **功能**: 详细的收入/支出记录，支持分类、账户、备注、时间
- **特色**: 动态分类网格、账户选择、表单验证
- **数据**: 完整的交易记录保存、账户余额更新

### 5. 转账记录 (transfer.html) ✅ 完整功能
- **功能**: 账户间资金转移，余额验证和更新
- **特色**: 双账户选择、余额检查、转账确认
- **数据**: 同时更新两个账户余额

### 6. 分类管理 (category-manage.html, add-category.html) ✅ 完整功能
- **功能**: 自定义收支分类，支持图标、颜色、预算设置
- **特色**: 类型切换、图标颜色选择器、预算设置
- **数据**: 分类增删改查、预算关联

### 7. 统计页面 (statistics.html) ✅ 数据集成
- **功能**: 实时收支统计、分类分析、时间段切换
- **特色**: 动态时间选择器、分类占比计算、趋势分析
- **数据**: 按时间段统计、分类排序、百分比计算

### 8. 预算页面 (budget.html)
- **功能**: 预算管理、分类预算、储蓄目标
- **特色**: 渐变色预算卡片、进度条显示、预算状态指示
- **交互**: 预算进度动画、状态颜色区分

### 9. 账户页面 (account.html) ✅ 数据集成
- **功能**: 实时账户管理、资产统计、账户列表
- **特色**: 动态总资产计算、账户统计、真实余额显示
- **数据**: 实时账户数据、月度收支统计

### 10. 账户管理 (account-manage.html, add-account.html) ✅ 完整功能
- **功能**: 账户增删改查、类型管理、图标颜色自定义
- **特色**: 账户类型选择、图标颜色选择器、余额管理
- **数据**: 完整的账户生命周期管理

### 11. 收支详情 (details.html) ✅ 数据集成
- **功能**: 实时交易记录查看、搜索筛选、按日期分组
- **特色**: 动态数据加载、多维度筛选、日期智能分组
- **数据**: 实时交易数据、智能搜索、日汇总计算

## 设计特色

### iOS设计规范
- **颜色**: 使用iOS系统色彩，包括蓝色、绿色、红色等
- **字体**: 采用苹方字体(PingFang SC)和系统字体
- **圆角**: 统一使用12px圆角半径
- **阴影**: 轻微阴影效果，符合iOS扁平化设计

### iPhone 15适配
- **尺寸**: 393×852px屏幕尺寸
- **状态栏**: 包含时间、信号、WiFi、电池等元素
- **Home Indicator**: 底部圆角指示器
- **安全区域**: 考虑刘海屏和圆角设计

### 交互体验
- **点击反馈**: 所有可点击元素都有视觉反馈
- **动画效果**: 进度条、按钮等有平滑动画
- **导航逻辑**: 清晰的页面跳转关系
- **浮动按钮**: 快速记账的FAB按钮

## 技术实现

### CSS特性
- **CSS Grid**: 用于网格布局
- **Flexbox**: 用于弹性布局
- **CSS变量**: 统一管理颜色和尺寸
- **渐变背景**: 卡片和按钮的渐变效果
- **响应式**: 支持不同屏幕尺寸

### JavaScript功能
- **标签栏切换**: 底部导航栏交互
- **表单交互**: 数字键盘、分类选择等
- **动画控制**: 进度条动画、点击效果
- **页面跳转**: 模拟真实的页面导航

## 使用方法

1. **本地预览**: 直接在浏览器中打开任意HTML文件
2. **推荐浏览器**: Chrome、Safari等现代浏览器
3. **最佳体验**: 使用浏览器的设备模拟器，选择iPhone尺寸
4. **起始页面**: 建议从index.html开始浏览

## 页面跳转关系

```
index.html (首页)
├── quick-record.html (快速记账)
├── details.html (收支详情)
└── 底部导航栏
    ├── record.html (记账)
    ├── statistics.html (统计)
    ├── budget.html (预算)
    └── account.html (账户)
```

## 数据功能特色

### 🔄 实时数据同步
- **自动更新**: 数据变更后所有相关页面自动刷新
- **余额计算**: 交易记录自动更新账户余额
- **统计实时**: 收支统计、分类分析实时计算
- **数据一致性**: 确保所有页面显示数据一致

### 💾 本地数据持久化
- **localStorage**: 数据保存在浏览器本地存储
- **数据恢复**: 刷新页面后数据自动恢复
- **默认数据**: 首次使用自动初始化示例数据
- **数据重置**: 支持重置为默认数据

### 🎨 完整的自定义功能
- **分类管理**: 自定义分类名称、图标、颜色、预算
- **账户管理**: 自定义账户名称、类型、图标、颜色
- **数据验证**: 完整的表单验证和错误处理
- **用户体验**: 流畅的交互动画和反馈

## 注意事项

1. **图片资源**: 使用FontAwesome图标库，需要网络连接
2. **字体加载**: 优先使用系统字体，确保最佳显示效果
3. **数据存储**: 使用localStorage，数据仅保存在当前浏览器
4. **功能完整**: 所有核心功能已实现，可进行完整的记账操作

## 后续开发建议

1. **数据持久化**: 集成本地存储或云端数据库
2. **用户认证**: 添加登录注册功能
3. **数据同步**: 支持多设备数据同步
4. **推送通知**: 预算提醒、记账提醒等

5. **更多图表**: 集成Chart.js等图表库
6. **手势操作**: 支持滑动删除、长按编辑等
7. **深色模式**: 支持iOS深色模式适配

---

**开发团队**: 财富管家产品团队  
**设计版本**: v1.0  
**更新日期**: 2024年1月
