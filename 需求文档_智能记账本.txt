智能记账本 iOS App 需求文档

一、用户细分
1. 主要用户群体：
   - 年轻白领（25-35岁）：注重理财和消费记录，追求便捷高效
   - 家庭主妇/主夫（30-45岁）：需要管理家庭开支，注重预算控制
   - 学生群体（18-25岁）：生活费管理，养成记账习惯
   - 小微企业主（30-50岁）：日常经营支出记录，简化财务管理

2. 用户特征：
   - 中文母语用户
   - 具备基本移动设备操作能力
   - 有一定的理财意识和记账需求
   - 希望简化记账流程，提高效率

二、核心功能

1. 记账功能
   - 用户点击"添加记录"按钮进入记账页面
   - 选择收入/支出类型
   - 输入金额（支持数字键盘）
   - 选择分类（餐饮、交通、购物、娱乐等）
   - 添加备注信息
   - 选择日期时间
   - 点击保存完成记录

2. 分类管理
   - 用户在"分类"页面查看所有分类
   - 点击"添加分类"创建新分类
   - 选择分类图标和颜色
   - 编辑或删除现有分类
   - 设置分类预算限额

3. 统计报表
   - 首页展示本月收支概览
   - 点击"统计"进入详细报表页面
   - 选择时间范围（日/周/月/年）
   - 查看收支趋势图表
   - 分类支出占比饼图
   - 预算执行情况

4. 账单管理
   - 用户在"账单"页面查看所有记录
   - 按日期/分类/金额排序
   - 搜索特定记录
   - 批量删除或编辑
   - 导出账单数据

5. 每月预算功能
   - 用户在"预算"页面设置月度总预算
   - 为不同分类设置独立预算额度
   - 实时显示预算使用情况和剩余额度
   - 预算超支时显示警告提醒
   - 查看历史月份预算执行情况
   - 预算vs实际支出对比分析


三、非功能需求

1. 色彩方案
   - 背景色：#f5f5f5
   - 主色调：#007AFF（苹果蓝）
   - 辅助色：#34C759（绿色-收入）、#FF3B30（红色-支出）
   - 文字色：#000000（主要文字）、#666666（次要文字）

2. 图标系统
   - 统一使用SF Symbols 4.0命名体系
   - 主要图标：
     - plus.circle.fill（添加记录）
     - chart.bar.xaxis（统计报表）
     - list.bullet（账单列表）
     - folder.fill（分类管理）
     - creditcard.fill（预算管理）
     - gearshape.fill（设置）
     - chevron.left（返回）
     - magnifyingglass（搜索）

3. 字体规范
   - 主字体：苹方-简(PingFang SC)
   - 标题：PingFang SC Bold 18pt
   - 正文：PingFang SC Regular 16pt
   - 次要文字：PingFang SC Regular 14pt
   - 数字：PingFang SC Medium 20pt

4. 布局规范
   - 页面最小边距：30px
   - 组件间距：20px
   - 行高：1.5倍字体大小
   - 按钮最小高度：44px
   - 圆角半径：8px

四、数据模型

1. 记录模型 (Record)
   伪代码：
   ```
   Record {
     id: UUID
     amount: Decimal
     type: Enum (income, expense)
     category: Category
     note: String
     date: Date
     createdAt: Date
     updatedAt: Date
   }
   ```

2. 分类模型 (Category)
   伪代码：
   ```
   Category {
     id: UUID
     name: String
     icon: String (SF Symbol名称)
     color: String (色值)
     type: Enum (income, expense)
     budget: Decimal (可选)
     isDefault: Boolean
     createdAt: Date
   }
   ```

3. 预算模型 (Budget)
   伪代码：
   ```
   Budget {
     id: UUID
     category: Category
     amount: Decimal
     period: Enum (monthly, yearly)
     startDate: Date
     endDate: Date
     isActive: Boolean
   }
   ```

4. 用户设置模型 (UserSettings)
   伪代码：
   ```
   UserSettings {
     id: UUID
     currency: String
     firstDayOfWeek: Enum
     budgetAlertEnabled: Boolean
     defaultCategory: Category
     autoBackup: Boolean
     lastBackupDate: Date
   }
   ```