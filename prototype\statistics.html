<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 统计</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .time-selector {
            display: flex;
            margin: 16px;
            background: var(--ios-white);
            border-radius: 12px;
            padding: 4px;
        }
        
        .time-option {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .time-option.active {
            background: var(--ios-blue);
            color: white;
        }
        
        .stats-overview {
            margin: 0 16px 16px;
            background: var(--ios-white);
            border-radius: 12px;
            padding: 20px;
        }
        
        .stats-period {
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--ios-secondary-label);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            text-align: center;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--ios-secondary-label);
        }
        
        .chart-container {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 0 16px 16px;
            padding: 20px;
            text-align: center;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .pie-chart {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            margin: 0 auto 16px;
            background: conic-gradient(
                #FF6B6B 0deg 108deg,
                #4ECDC4 108deg 180deg,
                #45B7D1 180deg 252deg,
                #96CEB4 252deg 288deg,
                #FFEAA7 288deg 360deg
            );
            position: relative;
        }
        
        .pie-chart::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
            background: var(--ios-white);
            border-radius: 50%;
        }
        
        .chart-legend {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
        }
        
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 8px;
        }
        
        .legend-left {
            display: flex;
            align-items: center;
        }
        
        .trend-chart {
            height: 120px;
            background: linear-gradient(to right, 
                rgba(0, 122, 255, 0.1) 0%, 
                rgba(0, 122, 255, 0.05) 100%);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .trend-line {
            position: absolute;
            bottom: 20px;
            left: 10px;
            right: 10px;
            height: 2px;
            background: var(--ios-blue);
            border-radius: 1px;
        }
        
        .trend-line::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 20%;
            width: 4px;
            height: 4px;
            background: var(--ios-blue);
            border-radius: 50%;
        }
        
        .trend-line::after {
            content: '';
            position: absolute;
            top: -40px;
            right: 30%;
            width: 4px;
            height: 4px;
            background: var(--ios-blue);
            border-radius: 50%;
        }
        
        .category-stats {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 0 16px 16px;
            overflow: hidden;
        }
        
        .category-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-icon {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            color: white;
        }
        
        .category-info {
            flex: 1;
        }
        
        .category-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .category-percent {
            font-size: 12px;
            color: var(--ios-secondary-label);
        }
        
        .category-amount {
            font-size: 16px;
            font-weight: 600;
            color: var(--ios-red);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-calendar"></i>
                    </a>
                </div>
                <div class="navbar-title">统计</div>
                <div class="navbar-right">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-share"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 时间选择器 -->
                <div class="time-selector">
                    <div class="time-option" data-period="week">本周</div>
                    <div class="time-option active" data-period="month">本月</div>
                    <div class="time-option" data-period="year">本年</div>
                </div>

                <!-- 统计概览 -->
                <div class="stats-overview">
                    <div class="stats-period">2024年1月</div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value text-success">¥8,500</div>
                            <div class="stat-label">收入</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value text-danger">¥6,350</div>
                            <div class="stat-label">支出</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value text-primary">¥2,150</div>
                            <div class="stat-label">结余</div>
                        </div>
                    </div>
                </div>

                <!-- 趋势图表 -->
                <div class="chart-container">
                    <div class="chart-title">支出趋势</div>
                    <div class="trend-chart">
                        <div class="trend-line"></div>
                    </div>
                </div>

                <!-- 分类占比 -->
                <div class="chart-container">
                    <div class="chart-title">支出分类占比</div>
                    <div class="pie-chart"></div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-left">
                                <div class="legend-color" style="background: #FF6B6B;"></div>
                                <span>餐饮</span>
                            </div>
                            <span>30%</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-left">
                                <div class="legend-color" style="background: #4ECDC4;"></div>
                                <span>交通</span>
                            </div>
                            <span>20%</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-left">
                                <div class="legend-color" style="background: #45B7D1;"></div>
                                <span>购物</span>
                            </div>
                            <span>20%</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-left">
                                <div class="legend-color" style="background: #96CEB4;"></div>
                                <span>娱乐</span>
                            </div>
                            <span>10%</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-left">
                                <div class="legend-color" style="background: #FFEAA7;"></div>
                                <span>其他</span>
                            </div>
                            <span>20%</span>
                        </div>
                    </div>
                </div>

                <!-- 分类详情 -->
                <div class="category-stats">
                    <div class="category-item">
                        <div class="category-icon" style="background: #FF6B6B;">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="category-info">
                            <div class="category-name">餐饮</div>
                            <div class="category-percent">30% · 15笔</div>
                        </div>
                        <div class="category-amount">¥1,905</div>
                    </div>
                    
                    <div class="category-item">
                        <div class="category-icon" style="background: #4ECDC4;">
                            <i class="fas fa-subway"></i>
                        </div>
                        <div class="category-info">
                            <div class="category-name">交通</div>
                            <div class="category-percent">20% · 22笔</div>
                        </div>
                        <div class="category-amount">¥1,270</div>
                    </div>
                    
                    <div class="category-item">
                        <div class="category-icon" style="background: #45B7D1;">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div class="category-info">
                            <div class="category-name">购物</div>
                            <div class="category-percent">20% · 8笔</div>
                        </div>
                        <div class="category-amount">¥1,270</div>
                    </div>
                    
                    <div class="category-item">
                        <div class="category-icon" style="background: #96CEB4;">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <div class="category-info">
                            <div class="category-name">娱乐</div>
                            <div class="category-percent">10% · 5笔</div>
                        </div>
                        <div class="category-amount">¥635</div>
                    </div>
                    
                    <div class="category-item">
                        <div class="category-icon" style="background: #FFEAA7;">
                            <i class="fas fa-ellipsis-h"></i>
                        </div>
                        <div class="category-info">
                            <div class="category-name">其他</div>
                            <div class="category-percent">20% · 12笔</div>
                        </div>
                        <div class="category-amount">¥1,270</div>
                    </div>
                </div>

                <!-- 更多统计选项 -->
                <div class="list-group">
                    <a href="monthly-report.html" class="list-item">
                        <div class="list-icon" style="background: var(--ios-blue);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">月度报表</div>
                            <div class="list-subtitle">详细的月度财务分析</div>
                        </div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                    <a href="yearly-summary.html" class="list-item">
                        <div class="list-icon" style="background: var(--ios-green);">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">年度总结</div>
                            <div class="list-subtitle">年度财务回顾与总结</div>
                        </div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                    <a href="category-analysis.html" class="list-item">
                        <div class="list-icon" style="background: var(--ios-purple);">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">分类分析</div>
                            <div class="list-subtitle">各分类消费深度分析</div>
                        </div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                </div>
            </div>

            <!-- 底部标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-home"></i></div>
                    <div>首页</div>
                </a>
                <a href="record.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-plus-circle"></i></div>
                    <div>记账</div>
                </a>
                <a href="statistics.html" class="tab-item active">
                    <div class="tab-icon"><i class="fas fa-chart-bar"></i></div>
                    <div>统计</div>
                </a>
                <a href="budget.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-wallet"></i></div>
                    <div>预算</div>
                </a>
                <a href="account.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-credit-card"></i></div>
                    <div>账户</div>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 时间选择器
            const timeOptions = document.querySelectorAll('.time-option');
            timeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    timeOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    
                    const period = this.getAttribute('data-period');
                    updateStatistics(period);
                });
            });

            // 标签栏切换
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const href = this.getAttribute('href');
                    if (href && href !== '#') {
                        window.location.href = href;
                    }
                });
            });

            function updateStatistics(period) {
                // 这里可以根据选择的时间段更新统计数据
                const periodElement = document.querySelector('.stats-period');
                switch(period) {
                    case 'week':
                        periodElement.textContent = '本周 (1/15 - 1/21)';
                        break;
                    case 'month':
                        periodElement.textContent = '2024年1月';
                        break;
                    case 'year':
                        periodElement.textContent = '2024年';
                        break;
                }
            }
        });
    </script>
</body>
</html>
