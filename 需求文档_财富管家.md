# 财富管家 iOS App 需求文档

## 一、用户细分

### 1. 主要用户群体：
- **理财新手（22-30岁）**：刚开始工作，需要建立理财习惯，关注消费分析和储蓄目标
- **精明消费者（28-40岁）**：有一定经济基础，注重消费优化和投资规划
- **家庭财务管理者（30-45岁）**：管理家庭收支，制定家庭预算和长期财务规划
- **小企业主（25-50岁）**：需要分离个人和经营账目，简化财务记录

### 2. 用户特征：
- 追求简洁高效的记账体验
- 希望获得智能化的财务建议
- 注重数据安全和隐私保护
- 有多账户管理需求
- 期望可视化的财务分析

## 二、页面层级结构

### 应用结构：
```
├─ L1: [首页]
│  ├─ L2: [快速记账]
│  └─ L2: [收支详情]
│   
├─ L1: [记账]
│  ├─ L2: [添加收入]
│  ├─ L2: [添加支出]
│  ├─ L2: [转账记录]
│  └─ L2: [分类管理]
│     ├─ L3: [新建分类]
│     └─ L3: [编辑分类]
├─ L1: [统计]
│  ├─ L2: [月度报表]
│  ├─ L2: [年度总结]
│  └─ L2: [分类分析]
│  
├─ L1: [预算]
│  ├─ L2: [月度预算]
│  ├─ L2: [年度规划]
│  ├─ L2: [储蓄目标]
│  └─ L2: [预算提醒]
└─ L1: [账户]
   ├─ L2: [账户管理]
   │  ├─ L3: [添加账户]
   │  └─ L3: [账户详情]
   └─ L2: [账单导入]
 

```

### 层级属性说明表：

| 页面名称 | 层级 | 上级页面 | 下级页面 | 访问路径 |
|---------|------|----------|----------|----------|
| 首页 | L1 | - | 快速记账, 收支详情, 智能分析 | /home |
| 记账 | L1 | - | 添加收入, 添加支出, 转账记录, 分类管理 | /record |
| 统计 | L1 | - | 月度报表, 年度总结, 分类分析 | /statistics |
| 预算 | L1 | - | 月度预算, 年度规划, 储蓄目标, 预算提醒 | /budget |
| 账户 | L1 | - | 账户管理, 账单导入 | /account |
| 快速记账 | L2 | 首页 | - | /home/<USER>
| 收支详情 | L2 | 首页 | - | /home/<USER>
| 智能分析 | L2 | 首页 | - | /home/<USER>
| 添加收入 | L2 | 记账 | - | /record/income |
| 添加支出 | L2 | 记账 | - | /record/expense |
| 转账记录 | L2 | 记账 | - | /record/transfer |
| 分类管理 | L2 | 记账 | 新建分类, 编辑分类 | /record/category |
| 新建分类 | L3 | 分类管理 | - | /record/category/new |
| 编辑分类 | L3 | 分类管理 | - | /record/category/edit |
| 月度报表 | L2 | 统计 | - | /statistics/monthly |
| 年度总结 | L2 | 统计 | - | /statistics/yearly |
| 分类分析 | L2 | 统计 | - | /statistics/category |
| 趋势预测 | L2 | 统计 | - | /statistics/trend |
| 月度预算 | L2 | 预算 | - | /budget/monthly |
| 年度规划 | L2 | 预算 | - | /budget/yearly |
| 储蓄目标 | L2 | 预算 | - | /budget/savings |
| 预算提醒 | L2 | 预算 | - | /budget/alerts |
| 账户管理 | L2 | 账户 | 添加账户, 账户详情 | /account/manage |
| 添加账户 | L3 | 账户管理 | - | /account/manage/new |
| 账户详情 | L3 | 账户管理 | - | /account/manage/details |
| 账单导入 | L2 | 账户 | - | /account/import |

## 三、核心功能（UI交互流程）

### 1. 智能记账功能
- 用户在首页点击"快速记账"浮动按钮
- 用户输入金额，系统提供语音输入选项
- 选择支出/收入类型，系统显示对应分类图标
- 选择账户（现金、银行卡、支付宝等）
- 添加备注和标签，支持表情符号
- 点击保存，系统显示记账成功动画

### 2. 多账户管理
- 用户在"账户"页面查看所有账户余额
- 点击"添加账户"选择账户类型（银行卡、现金、电子钱包）
- 输入账户名称、初始余额和账户图标
- 支持账户间转账记录
- 实时同步各账户余额变化

### 3. 智能预算管理
- 用户在"预算"页面设置月度总预算
- 系统根据历史数据智能推荐各分类预算额度
- 实时显示预算使用进度条和剩余天数
- 预算即将超支时推送温馨提醒
- 支持设置储蓄目标，显示达成进度

### 4. 可视化统计分析
- 首页展示本月收支概览卡片
- "统计"页面提供多维度图表分析
- 支持按时间、分类、账户筛选数据
- 显示消费趋势曲线和分类占比饼图
- 提供同比、环比分析和消费建议


## 四、数据模型

### 1. 交易记录模型 (Transaction)
```javascript
Transaction {
  id: UUID
  amount: Decimal
  type: Enum (income, expense, transfer)
  category: Category
  account: Account
  targetAccount: Account (转账时使用)
  note: String
  tags: Array<String>
  location: GeoLocation
  receipt: Image (可选)
  date: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}
```

### 2. 账户模型 (Account)
```javascript
Account {
  id: UUID
  name: String
  type: Enum (cash, bank_card, e_wallet, credit_card)
  balance: Decimal
  icon: String
  color: String
  isDefault: Boolean
  isActive: Boolean
  createdAt: DateTime
}
```

### 3. 分类模型 (Category)
```javascript
Category {
  id: UUID
  name: String
  icon: String
  color: String
  type: Enum (income, expense)
  parentCategory: Category (可选)
  monthlyBudget: Decimal (可选)
  isDefault: Boolean
  sortOrder: Integer
  createdAt: DateTime
}
```

### 4. 预算模型 (Budget)
```javascript
Budget {
  id: UUID
  name: String
  totalAmount: Decimal
  period: Enum (monthly, yearly)
  categories: Array<CategoryBudget>
  startDate: Date
  endDate: Date
  alertThreshold: Decimal (0.8表示80%时提醒)
  isActive: Boolean
}

CategoryBudget {
  category: Category
  budgetAmount: Decimal
  spentAmount: Decimal
}
```

### 5. 储蓄目标模型 (SavingsGoal)
```javascript
SavingsGoal {
  id: UUID
  name: String
  targetAmount: Decimal
  currentAmount: Decimal
  targetDate: Date
  description: String
  icon: String
  isCompleted: Boolean
  createdAt: DateTime
}
```

## 五、应用信息

### 中文应用名称：财富管家
### 英文应用名称：WealthKeeper

### 应用商店描述：
**中文版本：**
财富管家是一款简洁高效的个人财务管理应用，帮助您轻松记录每一笔收支，智能分析消费习惯，制定合理预算计划。支持多账户管理、智能分类、可视化报表等功能，让理财变得简单有趣。无论是日常开销记录还是长期财务规划，财富管家都是您贴心的理财助手。

**英文版本：**
WealthKeeper is an intuitive personal finance management app that helps you effortlessly track income and expenses, analyze spending patterns, and create smart budgets. With multi-account support, intelligent categorization, and visual reports, managing your finances becomes simple and engaging. Whether tracking daily expenses or planning long-term financial goals, WealthKeeper is your trusted financial companion.
