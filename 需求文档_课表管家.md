# 课表管家 iOS App 需求文档

## 一、用户细分

### 1. 主要用户群体：
- **中学生（13-18岁）**：课程繁重，需要高效管理学习时间和作业
- **大学生（18-25岁）**：课程灵活多变，需要统筹安排学习和社团活动
- **研究生（22-28岁）**：课程与研究并重，需要平衡学术任务和项目进度
- **在职学习者（25-45岁）**：工作与学习双重压力，需要合理安排时间

### 2. 用户特征：
- 中文母语用户
- 具备基本移动设备操作能力
- 有明确的学习目标和时间管理需求
- 希望提高学习效率，减少遗漏重要事项

## 二、页面层级结构

### 应用结构：
```
├─ L1: [首页]
│  ├─ L2: [今日课程详情]
│  ├─ L2: [快速添加课程]
│  └─ L2: [本周概览]
├─ L1: [课程表]
│  ├─ L2: [周视图]
│  ├─ L2: [月视图]
│  ├─ L2: [添加课程]
│  │  ├─ L3: [选择时间]
│  │  ├─ L3: [选择教室]
│  │  └─ L3: [设置提醒]
│  └─ L2: [课程详情]
│     ├─ L3: [编辑课程]
│     ├─ L3: [课程笔记]
│     └─ L3: [相关作业]
├─ L1: [作业管理]
│  ├─ L2: [待完成作业]
│  ├─ L2: [已完成作业]
│  ├─ L2: [添加作业]
│  │  ├─ L3: [选择课程]
│  │  ├─ L3: [设置截止日期]
│  │  └─ L3: [设置优先级]
│  └─ L2: [作业详情]
│     ├─ L3: [编辑作业]
│     └─ L3: [添加附件]
├─ L1: [考试安排]
│  ├─ L2: [即将到来的考试]
│  ├─ L2: [考试历史]
│  ├─ L2: [添加考试]
│  │  ├─ L3: [选择科目]
│  │  ├─ L3: [设置考试时间]
│  │  └─ L3: [添加考试地点]
│  └─ L2: [考试详情]
│     ├─ L3: [编辑考试信息]
│     └─ L3: [复习计划]
└─ L1: [设置]
   ├─ L2: [学期设置]
   ├─ L2: [提醒设置]
   ├─ L2: [数据备份]
   └─ L2: [关于应用]
```

### 层级属性说明表：

| 页面名称 | 层级 | 上级页面 | 下级页面 | 访问路径 |
|---------|------|----------|----------|----------|
| 首页 | L1 | - | 今日课程详情, 快速添加课程, 本周概览 | /home |
| 今日课程详情 | L2 | 首页 | - | /home/<USER>
| 快速添加课程 | L2 | 首页 | - | /home/<USER>
| 本周概览 | L2 | 首页 | - | /home/<USER>
| 课程表 | L1 | - | 周视图, 月视图, 添加课程, 课程详情 | /schedule |
| 周视图 | L2 | 课程表 | - | /schedule/week |
| 月视图 | L2 | 课程表 | - | /schedule/month |
| 添加课程 | L2 | 课程表 | 选择时间, 选择教室, 设置提醒 | /schedule/add |
| 选择时间 | L3 | 添加课程 | - | /schedule/add/time |
| 选择教室 | L3 | 添加课程 | - | /schedule/add/room |
| 设置提醒 | L3 | 添加课程 | - | /schedule/add/reminder |
| 课程详情 | L2 | 课程表 | 编辑课程, 课程笔记, 相关作业 | /schedule/detail |
| 编辑课程 | L3 | 课程详情 | - | /schedule/detail/edit |
| 课程笔记 | L3 | 课程详情 | - | /schedule/detail/notes |
| 相关作业 | L3 | 课程详情 | - | /schedule/detail/homework |
| 作业管理 | L1 | - | 待完成作业, 已完成作业, 添加作业, 作业详情 | /homework |
| 待完成作业 | L2 | 作业管理 | - | /homework/pending |
| 已完成作业 | L2 | 作业管理 | - | /homework/completed |
| 添加作业 | L2 | 作业管理 | 选择课程, 设置截止日期, 设置优先级 | /homework/add |
| 选择课程 | L3 | 添加作业 | - | /homework/add/course |
| 设置截止日期 | L3 | 添加作业 | - | /homework/add/deadline |
| 设置优先级 | L3 | 添加作业 | - | /homework/add/priority |
| 作业详情 | L2 | 作业管理 | 编辑作业, 添加附件 | /homework/detail |
| 编辑作业 | L3 | 作业详情 | - | /homework/detail/edit |
| 添加附件 | L3 | 作业详情 | - | /homework/detail/attachment |
| 考试安排 | L1 | - | 即将到来的考试, 考试历史, 添加考试, 考试详情 | /exam |
| 即将到来的考试 | L2 | 考试安排 | - | /exam/upcoming |
| 考试历史 | L2 | 考试安排 | - | /exam/history |
| 添加考试 | L2 | 考试安排 | 选择科目, 设置考试时间, 添加考试地点 | /exam/add |
| 选择科目 | L3 | 添加考试 | - | /exam/add/subject |
| 设置考试时间 | L3 | 添加考试 | - | /exam/add/time |
| 添加考试地点 | L3 | 添加考试 | - | /exam/add/location |
| 考试详情 | L2 | 考试安排 | 编辑考试信息, 复习计划 | /exam/detail |
| 编辑考试信息 | L3 | 考试详情 | - | /exam/detail/edit |
| 复习计划 | L3 | 考试详情 | - | /exam/detail/study-plan |
| 设置 | L1 | - | 学期设置, 提醒设置, 数据备份, 关于应用 | /settings |
| 学期设置 | L2 | 设置 | - | /settings/semester |
| 提醒设置 | L2 | 设置 | - | /settings/notifications |
| 数据备份 | L2 | 设置 | - | /settings/backup |
| 关于应用 | L2 | 设置 | - | /settings/about |

## 三、核心功能

### 1. 课程管理功能
- 用户点击"添加课程"按钮进入课程创建页面
- 输入课程名称和教师信息
- 选择上课时间（星期、开始时间、结束时间）
- 选择上课地点（教室或在线链接）
- 设置重复模式（单次、每周、自定义）
- 添加课程备注信息
- 设置课前提醒时间
- 点击保存完成课程添加

### 2. 作业管理功能
- 用户在"作业管理"页面查看所有作业
- 点击"添加作业"创建新作业任务
- 选择关联的课程科目
- 输入作业标题和详细描述
- 设置截止日期和时间
- 选择优先级（高、中、低）
- 添加相关附件或链接
- 标记作业完成状态
- 查看作业完成统计

### 3. 考试安排功能
- 用户在"考试安排"页面管理所有考试
- 点击"添加考试"录入考试信息
- 选择考试科目和类型
- 设置考试日期、时间和地点
- 添加考试范围和重点内容
- 设置考前提醒和复习计划
- 查看考试倒计时
- 记录考试成绩和反思

### 4. 时间表视图功能
- 首页显示今日课程安排概览
- 周视图展示一周的详细课程表
- 月视图显示整月的重要事件
- 支持左右滑动切换不同时间段
- 点击具体时间段快速添加事件
- 长按课程可进行编辑或删除操作
- 支持拖拽调整课程时间

### 5. 智能提醒功能
- 用户在"提醒设置"中配置提醒偏好
- 设置课前提醒时间（5分钟、15分钟、30分钟等）
- 配置作业截止日期提醒
- 设置考试倒计时提醒
- 支持推送通知和应用内提醒
- 智能识别冲突时间并提示用户
- 根据用户习惯推荐最佳学习时间

## 四、数据模型

### 1. 课程模型 (Course)
```javascript
Course {
  id: UUID
  name: String
  teacher: String
  classroom: String
  dayOfWeek: Enum (monday, tuesday, ..., sunday)
  startTime: Time
  endTime: Time
  color: String
  notes: String
  reminderMinutes: Integer
  repeatType: Enum (once, weekly, custom)
  startDate: Date
  endDate: Date
  isActive: Boolean
  createdAt: Date
  updatedAt: Date
}
```

### 2. 作业模型 (Homework)
```javascript
Homework {
  id: UUID
  title: String
  description: String
  course: Course
  dueDate: Date
  priority: Enum (high, medium, low)
  status: Enum (pending, in_progress, completed)
  attachments: Array<String>
  estimatedHours: Integer
  actualHours: Integer
  completedAt: Date
  createdAt: Date
  updatedAt: Date
}
```

### 3. 考试模型 (Exam)
```javascript
Exam {
  id: UUID
  subject: String
  course: Course
  examDate: Date
  startTime: Time
  endTime: Time
  location: String
  examType: Enum (midterm, final, quiz, assignment)
  syllabus: String
  studyPlan: String
  score: Float
  totalScore: Float
  notes: String
  reminderSettings: Object
  createdAt: Date
  updatedAt: Date
}
```

### 4. 学期设置模型 (Semester)
```javascript
Semester {
  id: UUID
  name: String
  startDate: Date
  endDate: Date
  isActive: Boolean
  courses: Array<Course>
  holidays: Array<Date>
  createdAt: Date
}
```

### 5. 用户设置模型 (UserSettings)
```javascript
UserSettings {
  id: UUID
  defaultReminderMinutes: Integer
  weekStartDay: Enum (sunday, monday)
  timeFormat: Enum (12hour, 24hour)
  notificationEnabled: Boolean
  autoBackup: Boolean
  lastBackupDate: Date
  themeColor: String
  language: String
}
```

## 五、应用名称和描述

### 中文名称：课表管家
### 英文名称：Schedule Master

### 应用商店描述（中文）：
课表管家是一款专为学生设计的智能时间管理应用。轻松管理课程安排、作业任务和考试计划，让学习生活井井有条。支持多视图课程表、智能提醒、作业跟踪等功能，帮助您高效安排学习时间，从容应对学业挑战。简洁直观的界面设计，让时间管理变得简单有趣。

### 应用商店描述（英文）：
Schedule Master is an intelligent time management app designed for students. Easily manage course schedules, homework assignments, and exam plans to keep your academic life organized. Features include multi-view timetables, smart reminders, assignment tracking, and more to help you efficiently organize study time and confidently tackle academic challenges. Clean and intuitive interface design makes time management simple and enjoyable.
