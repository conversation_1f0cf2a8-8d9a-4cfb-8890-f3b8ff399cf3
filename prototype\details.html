<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 收支详情</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .filter-bar {
            display: flex;
            gap: 8px;
            padding: 16px;
            background: var(--ios-white);
            margin: 0 16px 16px;
            border-radius: 12px;
            overflow-x: auto;
        }
        
        .filter-item {
            padding: 8px 16px;
            border-radius: 20px;
            background: var(--ios-gray6);
            color: var(--ios-secondary-label);
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-item.active {
            background: var(--ios-blue);
            color: white;
        }
        
        .date-section {
            margin-bottom: 16px;
        }
        
        .date-header {
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 600;
            color: var(--ios-secondary-label);
            background: var(--ios-background);
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .transaction-list {
            background: var(--ios-white);
            margin: 0 16px;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .transaction-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 0.5px solid var(--ios-separator);
            cursor: pointer;
        }
        
        .transaction-item:last-child {
            border-bottom: none;
        }
        
        .transaction-item:hover {
            background: var(--ios-gray6);
        }
        
        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
        }
        
        .transaction-info {
            flex: 1;
        }
        
        .transaction-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .transaction-detail {
            font-size: 13px;
            color: var(--ios-secondary-label);
        }
        
        .transaction-amount {
            font-size: 16px;
            font-weight: 600;
            text-align: right;
        }
        
        .amount-income {
            color: var(--ios-green);
        }
        
        .amount-expense {
            color: var(--ios-red);
        }
        
        .day-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 16px;
            background: var(--ios-gray6);
            font-size: 13px;
            color: var(--ios-secondary-label);
        }
        
        .search-bar {
            margin: 16px;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 1px solid var(--ios-separator);
            border-radius: 12px;
            font-size: 16px;
            background: var(--ios-white);
        }
        
        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--ios-gray);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="index.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title">收支详情</div>
                <div class="navbar-right">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-filter"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索交易记录">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <!-- 筛选栏 -->
                <div class="filter-bar">
                    <div class="filter-item active">全部</div>
                    <div class="filter-item">支出</div>
                    <div class="filter-item">收入</div>
                    <div class="filter-item">转账</div>
                    <div class="filter-item">餐饮</div>
                    <div class="filter-item">交通</div>
                    <div class="filter-item">购物</div>
                </div>

                <!-- 今天 -->
                <div class="date-section">
                    <div class="date-header">今天 · 1月21日</div>
                    <div class="transaction-list">
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #FF6B6B;">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="transaction-info">
                                <div class="transaction-title">午餐</div>
                                <div class="transaction-detail">12:30 · 现金</div>
                            </div>
                            <div class="transaction-amount amount-expense">-¥35.00</div>
                        </div>
                        
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #4ECDC4;">
                                <i class="fas fa-subway"></i>
                            </div>
                            <div class="transaction-info">
                                <div class="transaction-title">地铁</div>
                                <div class="transaction-detail">09:15 · 交通卡</div>
                            </div>
                            <div class="transaction-amount amount-expense">-¥6.00</div>
                        </div>
                        
                        <div class="day-summary">
                            <span>今日支出</span>
                            <span class="amount-expense">-¥41.00</span>
                        </div>
                    </div>
                </div>

                <!-- 昨天 -->
                <div class="date-section">
                    <div class="date-header">昨天 · 1月20日</div>
                    <div class="transaction-list">
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: var(--ios-green);">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="transaction-info">
                                <div class="transaction-title">工资</div>
                                <div class="transaction-detail">10:00 · 工商银行</div>
                            </div>
                            <div class="transaction-amount amount-income">+¥8,500.00</div>
                        </div>
                        
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #45B7D1;">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <div class="transaction-info">
                                <div class="transaction-title">咖啡</div>
                                <div class="transaction-detail">15:20 · 支付宝</div>
                            </div>
                            <div class="transaction-amount amount-expense">-¥28.00</div>
                        </div>
                        
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #96CEB4;">
                                <i class="fas fa-gamepad"></i>
                            </div>
                            <div class="transaction-info">
                                <div class="transaction-title">电影票</div>
                                <div class="transaction-detail">19:30 · 微信支付</div>
                            </div>
                            <div class="transaction-amount amount-expense">-¥80.00</div>
                        </div>
                        
                        <div class="day-summary">
                            <span>昨日结余</span>
                            <span class="amount-income">+¥8,392.00</span>
                        </div>
                    </div>
                </div>

                <!-- 1月19日 -->
                <div class="date-section">
                    <div class="date-header">1月19日 · 周五</div>
                    <div class="transaction-list">
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #FF6B6B;">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="transaction-info">
                                <div class="transaction-title">晚餐</div>
                                <div class="transaction-detail">18:45 · 支付宝</div>
                            </div>
                            <div class="transaction-amount amount-expense">-¥65.00</div>
                        </div>
                        
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #4ECDC4;">
                                <i class="fas fa-taxi"></i>
                            </div>
                            <div class="transaction-info">
                                <div class="transaction-title">打车</div>
                                <div class="transaction-detail">21:30 · 微信支付</div>
                            </div>
                            <div class="transaction-amount amount-expense">-¥25.00</div>
                        </div>
                        
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #45B7D1;">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="transaction-info">
                                <div class="transaction-title">超市购物</div>
                                <div class="transaction-detail">16:20 · 现金</div>
                            </div>
                            <div class="transaction-amount amount-expense">-¥120.00</div>
                        </div>
                        
                        <div class="day-summary">
                            <span>当日支出</span>
                            <span class="amount-expense">-¥210.00</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选项切换
            const filterItems = document.querySelectorAll('.filter-item');
            filterItems.forEach(item => {
                item.addEventListener('click', function() {
                    filterItems.forEach(filter => filter.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 这里可以添加筛选逻辑
                    const filterType = this.textContent;
                    console.log('筛选类型:', filterType);
                });
            });

            // 交易项点击
            const transactionItems = document.querySelectorAll('.transaction-item');
            transactionItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.style.backgroundColor = 'var(--ios-gray6)';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 150);
                    
                    // 这里可以跳转到交易详情页面
                    console.log('查看交易详情');
                });
            });

            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                // 这里可以添加搜索逻辑
                console.log('搜索:', searchTerm);
            });
        });
    </script>
</body>
</html>
