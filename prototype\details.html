<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 收支详情</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .filter-bar {
            display: flex;
            gap: 8px;
            padding: 16px;
            background: var(--ios-white);
            margin: 0 16px 16px;
            border-radius: 12px;
            overflow-x: auto;
        }
        
        .filter-item {
            padding: 8px 16px;
            border-radius: 20px;
            background: var(--ios-gray6);
            color: var(--ios-secondary-label);
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .filter-item.active {
            background: var(--ios-blue);
            color: white;
        }
        
        .date-section {
            margin-bottom: 16px;
        }
        
        .date-header {
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 600;
            color: var(--ios-secondary-label);
            background: var(--ios-background);
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .transaction-list {
            background: var(--ios-white);
            margin: 0 16px;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .transaction-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 0.5px solid var(--ios-separator);
            cursor: pointer;
            position: relative;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-item:hover {
            background: var(--ios-gray6);
        }

        .transaction-actions {
            display: flex;
            gap: 8px;
            margin-left: 8px;
        }

        .action-button {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .edit-button {
            background: var(--ios-blue);
            color: white;
        }

        .delete-button {
            background: var(--ios-red);
            color: white;
        }

        .action-button:hover {
            transform: scale(1.1);
        }
        
        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
        }
        
        .transaction-info {
            flex: 1;
        }
        
        .transaction-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .transaction-detail {
            font-size: 13px;
            color: var(--ios-secondary-label);
        }
        
        .transaction-amount {
            font-size: 16px;
            font-weight: 600;
            text-align: right;
        }
        
        .amount-income {
            color: var(--ios-green);
        }
        
        .amount-expense {
            color: var(--ios-red);
        }
        
        .day-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 16px;
            background: var(--ios-gray6);
            font-size: 13px;
            color: var(--ios-secondary-label);
        }
        
        .search-bar {
            margin: 16px;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 1px solid var(--ios-separator);
            border-radius: 12px;
            font-size: 16px;
            background: var(--ios-white);
        }
        
        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--ios-gray);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="index.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title">收支详情</div>
                <div class="navbar-right">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-filter"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索交易记录">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <!-- 筛选栏 -->
                <div class="filter-bar">
                    <div class="filter-item active">全部</div>
                    <div class="filter-item">支出</div>
                    <div class="filter-item">收入</div>
                    <div class="filter-item">转账</div>
                    <div class="filter-item">餐饮</div>
                    <div class="filter-item">交通</div>
                    <div class="filter-item">购物</div>
                </div>

                <!-- 交易记录将通过JavaScript动态加载 -->
                <div id="transactionsByDate">
                    <!-- 按日期分组的交易记录 -->
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="data-manager.js"></script>
    <script>
        let currentFilter = 'all';
        let searchTerm = '';

        document.addEventListener('DOMContentLoaded', function() {
            loadTransactions();
            bindEvents();
        });

        function loadTransactions() {
            const transactions = dataManager.getTransactions();
            const filteredTransactions = filterTransactions(transactions);
            displayTransactionsByDate(filteredTransactions);
        }

        function filterTransactions(transactions) {
            let filtered = transactions;

            // 按类型筛选
            if (currentFilter !== 'all') {
                if (currentFilter === '支出') {
                    filtered = filtered.filter(t => t.type === 'expense');
                } else if (currentFilter === '收入') {
                    filtered = filtered.filter(t => t.type === 'income');
                } else if (currentFilter === '转账') {
                    filtered = filtered.filter(t => t.type === 'transfer');
                } else {
                    // 按分类筛选
                    const category = dataManager.getCategories().find(cat => cat.name === currentFilter);
                    if (category) {
                        filtered = filtered.filter(t => t.categoryId === category.id);
                    }
                }
            }

            // 按搜索词筛选
            if (searchTerm) {
                filtered = filtered.filter(t => {
                    const category = dataManager.getCategories().find(cat => cat.id === t.categoryId);
                    const account = dataManager.getAccount(t.accountId);
                    return (t.note && t.note.toLowerCase().includes(searchTerm)) ||
                           (category && category.name.toLowerCase().includes(searchTerm)) ||
                           (account && account.name.toLowerCase().includes(searchTerm));
                });
            }

            return filtered;
        }

        function displayTransactionsByDate(transactions) {
            const container = document.getElementById('transactionsByDate');
            container.innerHTML = '';

            // 按日期分组
            const groupedTransactions = groupTransactionsByDate(transactions);

            Object.keys(groupedTransactions).forEach(dateKey => {
                const dayTransactions = groupedTransactions[dateKey];
                const dateSection = createDateSection(dateKey, dayTransactions);
                container.appendChild(dateSection);
            });
        }

        function groupTransactionsByDate(transactions) {
            const grouped = {};

            transactions.forEach(transaction => {
                const date = new Date(transaction.date);
                const dateKey = date.toDateString();

                if (!grouped[dateKey]) {
                    grouped[dateKey] = [];
                }
                grouped[dateKey].push(transaction);
            });

            return grouped;
        }

        function createDateSection(dateKey, transactions) {
            const date = new Date(dateKey);
            const dateSection = document.createElement('div');
            dateSection.className = 'date-section';

            // 创建日期标题
            const dateHeader = document.createElement('div');
            dateHeader.className = 'date-header';
            dateHeader.textContent = formatDateHeader(date);

            // 创建交易列表
            const transactionList = document.createElement('div');
            transactionList.className = 'transaction-list';

            transactions.forEach(transaction => {
                const transactionElement = createTransactionElement(transaction);
                transactionList.appendChild(transactionElement);
            });

            // 创建日汇总
            const daySummary = createDaySummary(transactions);
            transactionList.appendChild(daySummary);

            dateSection.appendChild(dateHeader);
            dateSection.appendChild(transactionList);

            return dateSection;
        }

        function createTransactionElement(transaction) {
            const category = dataManager.getCategories().find(cat => cat.id === transaction.categoryId);
            const account = dataManager.getAccount(transaction.accountId);

            const transactionElement = document.createElement('div');
            transactionElement.className = 'transaction-item';

            const transactionDate = new Date(transaction.date);
            const timeString = transactionDate.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            transactionElement.innerHTML = `
                <div class="transaction-icon" style="background: ${category ? category.color : '#8E8E93'};">
                    <i class="${category ? category.icon : 'fas fa-question'}"></i>
                </div>
                <div class="transaction-info">
                    <div class="transaction-title">${transaction.note || (category ? category.name : '未知分类')}</div>
                    <div class="transaction-detail">${timeString} · ${account ? account.name : '未知账户'}</div>
                </div>
                <div class="transaction-amount ${transaction.type === 'income' ? 'amount-income' : 'amount-expense'}">
                    ${transaction.type === 'income' ? '+' : '-'}¥${transaction.amount.toFixed(2)}
                </div>
                <div class="transaction-actions">
                    <button class="action-button edit-button" onclick="editTransaction('${transaction.id}', event)">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-button delete-button" onclick="deleteTransaction('${transaction.id}', event)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            transactionElement.addEventListener('click', function(e) {
                // 如果点击的是操作按钮，不触发行点击事件
                if (!e.target.closest('.transaction-actions')) {
                    this.style.backgroundColor = 'var(--ios-gray6)';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 150);
                }
            });

            return transactionElement;
        }

        function createDaySummary(transactions) {
            const income = transactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
            const expense = transactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
            const balance = income - expense;

            const summaryElement = document.createElement('div');
            summaryElement.className = 'day-summary';

            let summaryText, summaryClass;
            if (balance > 0) {
                summaryText = '当日结余';
                summaryClass = 'amount-income';
            } else if (balance < 0) {
                summaryText = '当日支出';
                summaryClass = 'amount-expense';
            } else {
                summaryText = '当日收支平衡';
                summaryClass = '';
            }

            summaryElement.innerHTML = `
                <span>${summaryText}</span>
                <span class="${summaryClass}">${balance >= 0 ? '+' : ''}¥${Math.abs(balance).toFixed(2)}</span>
            `;

            return summaryElement;
        }

        function formatDateHeader(date) {
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            if (date.toDateString() === today.toDateString()) {
                return `今天 · ${date.getMonth() + 1}月${date.getDate()}日`;
            } else if (date.toDateString() === yesterday.toDateString()) {
                return `昨天 · ${date.getMonth() + 1}月${date.getDate()}日`;
            } else {
                const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                return `${date.getMonth() + 1}月${date.getDate()}日 · ${weekdays[date.getDay()]}`;
            }
        }

        function bindEvents() {
            // 筛选项切换
            const filterItems = document.querySelectorAll('.filter-item');
            filterItems.forEach(item => {
                item.addEventListener('click', function() {
                    filterItems.forEach(filter => filter.classList.remove('active'));
                    this.classList.add('active');

                    currentFilter = this.textContent;
                    loadTransactions();
                });
            });

            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('input', function() {
                searchTerm = this.value.toLowerCase();
                loadTransactions();
            });
        }

        // 编辑交易记录
        function editTransaction(transactionId, event) {
            event.stopPropagation();
            const transaction = dataManager.getTransaction(transactionId);
            if (!transaction) return;

            // 跳转到编辑页面，根据交易类型选择不同的编辑页面
            if (transaction.type === 'income') {
                window.location.href = `add-income.html?edit=${transactionId}`;
            } else if (transaction.type === 'expense') {
                window.location.href = `add-expense.html?edit=${transactionId}`;
            }
        }

        // 删除交易记录
        function deleteTransaction(transactionId, event) {
            event.stopPropagation();
            const transaction = dataManager.getTransaction(transactionId);
            if (!transaction) return;

            const category = dataManager.getCategory(transaction.categoryId);
            const categoryName = category ? category.name : '未知分类';

            if (confirm(`确定要删除这笔${transaction.type === 'income' ? '收入' : '支出'}记录吗？\n\n分类：${categoryName}\n金额：¥${transaction.amount.toFixed(2)}\n备注：${transaction.note || '无'}`)) {
                try {
                    dataManager.deleteTransaction(transactionId);
                    loadTransactions();

                    // 显示成功提示
                    const toast = document.createElement('div');
                    toast.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: rgba(0, 0, 0, 0.8);
                        color: white;
                        padding: 12px 20px;
                        border-radius: 8px;
                        font-size: 14px;
                        z-index: 1000;
                    `;
                    toast.textContent = '交易记录已删除';
                    document.body.appendChild(toast);

                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 2000);
                } catch (error) {
                    alert('删除失败，请重试');
                    console.error('删除交易失败:', error);
                }
            }
        }

        // 监听数据变化
        window.addEventListener('dataChanged', function() {
            loadTransactions();
        });
    </script>
</body>
</html>
