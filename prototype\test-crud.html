<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRUD功能测试</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-section {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 16px;
            padding: 16px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--ios-blue);
        }
        
        .test-button {
            display: block;
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
        }
        
        .test-button:hover {
            background: var(--ios-blue-dark);
        }
        
        .test-result {
            margin-top: 12px;
            padding: 8px;
            background: var(--ios-gray6);
            border-radius: 6px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="index.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title">CRUD功能测试</div>
                <div class="navbar-right"></div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 数据管理器测试 -->
                <div class="test-section">
                    <div class="test-title">数据管理器API测试</div>
                    <button class="test-button" onclick="testDataManager()">测试所有CRUD方法</button>
                    <div class="test-result" id="dataManagerResult"></div>
                </div>

                <!-- 页面跳转测试 -->
                <div class="test-section">
                    <div class="test-title">编辑页面跳转测试</div>
                    <button class="test-button" onclick="testEditPages()">测试编辑页面跳转</button>
                    <div class="test-result" id="editPagesResult"></div>
                </div>

                <!-- 快速导航 -->
                <div class="test-section">
                    <div class="test-title">快速导航</div>
                    <button class="test-button" onclick="location.href='details.html'">收支详情（含编辑删除）</button>
                    <button class="test-button" onclick="location.href='category-manage.html'">分类管理（含编辑删除）</button>
                    <button class="test-button" onclick="location.href='account-manage.html'">账户管理（含编辑删除）</button>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="data-manager.js"></script>
    <script>
        function testDataManager() {
            const resultDiv = document.getElementById('dataManagerResult');
            let results = [];
            
            try {
                // 测试分类CRUD
                results.push('=== 分类CRUD测试 ===');
                
                // 添加测试分类
                const testCategory = dataManager.addCategory({
                    name: '测试分类',
                    type: 'expense',
                    icon: 'fas fa-test',
                    color: '#FF0000',
                    budget: 100
                });
                results.push(`✅ 添加分类: ${testCategory.name} (ID: ${testCategory.id})`);
                
                // 更新分类
                const updatedCategory = dataManager.updateCategory(testCategory.id, {
                    name: '更新后的测试分类',
                    budget: 200
                });
                results.push(`✅ 更新分类: ${updatedCategory.name} (预算: ${updatedCategory.budget})`);
                
                // 获取分类
                const retrievedCategory = dataManager.getCategory(testCategory.id);
                results.push(`✅ 获取分类: ${retrievedCategory.name}`);
                
                // 删除分类
                const deleteResult = dataManager.deleteCategory(testCategory.id);
                results.push(`✅ 删除分类: ${deleteResult ? '成功' : '失败'}`);
                
                results.push('');
                
                // 测试账户CRUD
                results.push('=== 账户CRUD测试 ===');
                
                // 添加测试账户
                const testAccount = dataManager.addAccount({
                    name: '测试账户',
                    type: 'bank_card',
                    balance: 1000,
                    icon: 'fas fa-test',
                    color: '#00FF00'
                });
                results.push(`✅ 添加账户: ${testAccount.name} (余额: ${testAccount.balance})`);
                
                // 更新账户
                const updatedAccount = dataManager.updateAccount(testAccount.id, {
                    name: '更新后的测试账户',
                    balance: 2000
                });
                results.push(`✅ 更新账户: ${updatedAccount.name} (余额: ${updatedAccount.balance})`);
                
                // 删除账户
                const accountDeleteResult = dataManager.deleteAccount(testAccount.id);
                results.push(`✅ 删除账户: ${accountDeleteResult ? '成功' : '失败'}`);
                
                results.push('');
                
                // 测试交易CRUD
                results.push('=== 交易CRUD测试 ===');
                
                // 添加测试交易
                const testTransaction = dataManager.addTransaction({
                    amount: 50,
                    type: 'expense',
                    categoryId: '1', // 使用默认分类
                    accountId: '1',  // 使用默认账户
                    note: '测试交易',
                    date: new Date().toISOString()
                });
                results.push(`✅ 添加交易: ${testTransaction.note} (金额: ${testTransaction.amount})`);
                
                // 更新交易
                const updatedTransaction = dataManager.updateTransaction(testTransaction.id, {
                    amount: 75,
                    note: '更新后的测试交易'
                });
                results.push(`✅ 更新交易: ${updatedTransaction.note} (金额: ${updatedTransaction.amount})`);
                
                // 获取交易
                const retrievedTransaction = dataManager.getTransaction(testTransaction.id);
                results.push(`✅ 获取交易: ${retrievedTransaction.note}`);
                
                // 删除交易
                const transactionDeleteResult = dataManager.deleteTransaction(testTransaction.id);
                results.push(`✅ 删除交易: ${transactionDeleteResult ? '成功' : '失败'}`);
                
                results.push('');
                results.push('🎉 所有CRUD测试完成！');
                
            } catch (error) {
                results.push(`❌ 测试失败: ${error.message}`);
                console.error('测试错误:', error);
            }
            
            resultDiv.innerHTML = results.join('<br>');
        }
        
        function testEditPages() {
            const resultDiv = document.getElementById('editPagesResult');
            let results = [];
            
            // 获取一些测试数据
            const categories = dataManager.getCategories();
            const accounts = dataManager.getAccounts();
            const transactions = dataManager.getTransactions();
            
            if (categories.length > 0) {
                const categoryId = categories[0].id;
                results.push(`<a href="add-category.html?edit=${categoryId}" target="_blank">编辑分类: ${categories[0].name}</a>`);
            }
            
            if (accounts.length > 0) {
                const accountId = accounts[0].id;
                results.push(`<a href="add-account.html?edit=${accountId}" target="_blank">编辑账户: ${accounts[0].name}</a>`);
            }
            
            if (transactions.length > 0) {
                const transaction = transactions[0];
                const editUrl = transaction.type === 'income' ? 'add-income.html' : 'add-expense.html';
                results.push(`<a href="${editUrl}?edit=${transaction.id}" target="_blank">编辑交易: ${transaction.note || '无备注'}</a>`);
            }
            
            if (results.length === 0) {
                results.push('没有可编辑的数据，请先添加一些数据');
            }
            
            resultDiv.innerHTML = results.join('<br>');
        }
    </script>
</body>
</html>
