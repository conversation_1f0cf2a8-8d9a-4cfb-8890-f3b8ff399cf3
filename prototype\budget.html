<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 预算</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .budget-overview {
            background: linear-gradient(135deg, var(--ios-purple), #C44569);
            color: white;
            margin: 16px;
            border-radius: 16px;
            padding: 24px;
        }
        
        .budget-title {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 8px;
        }
        
        .budget-amount {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        .budget-progress {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            height: 8px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .budget-progress-fill {
            height: 100%;
            background: white;
            border-radius: 8px;
            width: 65%;
            transition: width 0.3s ease;
        }
        
        .budget-info {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .budget-categories {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 0 16px 16px;
            overflow: hidden;
        }
        
        .category-budget {
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .category-budget:last-child {
            border-bottom: none;
        }
        
        .category-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .category-icon {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            color: white;
        }
        
        .category-info {
            flex: 1;
        }
        
        .category-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .category-amounts {
            font-size: 13px;
            color: var(--ios-secondary-label);
        }
        
        .category-status {
            font-size: 14px;
            font-weight: 600;
        }
        
        .status-safe { color: var(--ios-green); }
        .status-warning { color: var(--ios-orange); }
        .status-danger { color: var(--ios-red); }
        
        .category-progress {
            height: 6px;
            background: var(--ios-gray5);
            border-radius: 3px;
            overflow: hidden;
        }
        
        .category-progress-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .progress-safe { background: var(--ios-green); }
        .progress-warning { background: var(--ios-orange); }
        .progress-danger { background: var(--ios-red); }
        
        .savings-goals {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 0 16px 16px;
            overflow: hidden;
        }
        
        .goal-item {
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .goal-item:last-child {
            border-bottom: none;
        }
        
        .goal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .goal-name {
            font-size: 16px;
            font-weight: 600;
        }
        
        .goal-target {
            font-size: 14px;
            color: var(--ios-secondary-label);
        }
        
        .goal-progress {
            height: 8px;
            background: var(--ios-gray5);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .goal-progress-fill {
            height: 100%;
            background: var(--ios-blue);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .goal-info {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            color: var(--ios-secondary-label);
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin: 0 16px 16px;
        }
        
        .action-card {
            background: var(--ios-white);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            text-decoration: none;
            color: var(--ios-label);
            transition: transform 0.2s;
        }
        
        .action-card:hover {
            transform: scale(0.98);
        }
        
        .action-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 20px;
            color: white;
        }
        
        .action-title {
            font-size: 14px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-calendar"></i>
                    </a>
                </div>
                <div class="navbar-title">预算</div>
                <div class="navbar-right">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 预算概览 -->
                <div class="budget-overview">
                    <div class="budget-title">本月预算</div>
                    <div class="budget-amount">¥5,000</div>
                    <div class="budget-progress">
                        <div class="budget-progress-fill"></div>
                    </div>
                    <div class="budget-info">
                        <span>已使用 ¥3,250</span>
                        <span>剩余 ¥1,750</span>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="quick-actions">
                    <a href="monthly-budget.html" class="action-card">
                        <div class="action-icon" style="background: var(--ios-blue);">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="action-title">月度预算</div>
                    </a>
                    <a href="savings-goal.html" class="action-card">
                        <div class="action-icon" style="background: var(--ios-green);">
                            <i class="fas fa-piggy-bank"></i>
                        </div>
                        <div class="action-title">储蓄目标</div>
                    </a>
                </div>

                <!-- 分类预算 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">分类预算</div>
                    </div>
                </div>
                
                <div class="budget-categories">
                    <div class="category-budget">
                        <div class="category-header">
                            <div class="category-icon" style="background: #FF6B6B;">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-name">餐饮</div>
                                <div class="category-amounts">¥800 / ¥1,200</div>
                            </div>
                            <div class="category-status status-warning">67%</div>
                        </div>
                        <div class="category-progress">
                            <div class="category-progress-fill progress-warning" style="width: 67%;"></div>
                        </div>
                    </div>
                    
                    <div class="category-budget">
                        <div class="category-header">
                            <div class="category-icon" style="background: #4ECDC4;">
                                <i class="fas fa-subway"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-name">交通</div>
                                <div class="category-amounts">¥320 / ¥500</div>
                            </div>
                            <div class="category-status status-safe">64%</div>
                        </div>
                        <div class="category-progress">
                            <div class="category-progress-fill progress-safe" style="width: 64%;"></div>
                        </div>
                    </div>
                    
                    <div class="category-budget">
                        <div class="category-header">
                            <div class="category-icon" style="background: #45B7D1;">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-name">购物</div>
                                <div class="category-amounts">¥950 / ¥800</div>
                            </div>
                            <div class="category-status status-danger">119%</div>
                        </div>
                        <div class="category-progress">
                            <div class="category-progress-fill progress-danger" style="width: 100%;"></div>
                        </div>
                    </div>
                    
                    <div class="category-budget">
                        <div class="category-header">
                            <div class="category-icon" style="background: #96CEB4;">
                                <i class="fas fa-gamepad"></i>
                            </div>
                            <div class="category-info">
                                <div class="category-name">娱乐</div>
                                <div class="category-amounts">¥180 / ¥600</div>
                            </div>
                            <div class="category-status status-safe">30%</div>
                        </div>
                        <div class="category-progress">
                            <div class="category-progress-fill progress-safe" style="width: 30%;"></div>
                        </div>
                    </div>
                </div>

                <!-- 储蓄目标 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">储蓄目标</div>
                    </div>
                </div>
                
                <div class="savings-goals">
                    <div class="goal-item">
                        <div class="goal-header">
                            <div class="goal-name">旅行基金</div>
                            <div class="goal-target">目标: ¥10,000</div>
                        </div>
                        <div class="goal-progress">
                            <div class="goal-progress-fill" style="width: 45%;"></div>
                        </div>
                        <div class="goal-info">
                            <span>已存: ¥4,500</span>
                            <span>还需: ¥5,500</span>
                        </div>
                    </div>
                    
                    <div class="goal-item">
                        <div class="goal-header">
                            <div class="goal-name">应急基金</div>
                            <div class="goal-target">目标: ¥20,000</div>
                        </div>
                        <div class="goal-progress">
                            <div class="goal-progress-fill" style="width: 25%;"></div>
                        </div>
                        <div class="goal-info">
                            <span>已存: ¥5,000</span>
                            <span>还需: ¥15,000</span>
                        </div>
                    </div>
                </div>

                <!-- 更多预算功能 -->
                <div class="list-group">
                    <a href="yearly-planning.html" class="list-item">
                        <div class="list-icon" style="background: var(--ios-orange);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">年度规划</div>
                            <div class="list-subtitle">制定年度财务计划</div>
                        </div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                    <a href="budget-alerts.html" class="list-item">
                        <div class="list-icon" style="background: var(--ios-red);">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">预算提醒</div>
                            <div class="list-subtitle">设置预算超支提醒</div>
                        </div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                </div>
            </div>

            <!-- 底部标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-home"></i></div>
                    <div>首页</div>
                </a>
                <a href="record.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-plus-circle"></i></div>
                    <div>记账</div>
                </a>
                <a href="statistics.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-chart-bar"></i></div>
                    <div>统计</div>
                </a>
                <a href="budget.html" class="tab-item active">
                    <div class="tab-icon"><i class="fas fa-wallet"></i></div>
                    <div>预算</div>
                </a>
                <a href="account.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-credit-card"></i></div>
                    <div>账户</div>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签栏切换
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const href = this.getAttribute('href');
                    if (href && href !== '#') {
                        window.location.href = href;
                    }
                });
            });

            // 动画效果
            const progressBars = document.querySelectorAll('.category-progress-fill, .goal-progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 300);
            });

            // 预算卡片点击效果
            const actionCards = document.querySelectorAll('.action-card');
            actionCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
