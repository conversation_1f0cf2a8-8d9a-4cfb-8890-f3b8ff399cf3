<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 分类管理</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .type-selector {
            display: flex;
            margin: 16px;
            background: var(--ios-white);
            border-radius: 12px;
            padding: 4px;
        }
        
        .type-option {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .type-option.active {
            background: var(--ios-blue);
            color: white;
        }
        
        .category-list {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 0 16px 16px;
            overflow: hidden;
        }
        
        .category-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
        }
        
        .category-info {
            flex: 1;
        }
        
        .category-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .category-budget {
            font-size: 13px;
            color: var(--ios-secondary-label);
        }
        
        .category-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-button {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
        }
        
        .edit-button {
            background: var(--ios-blue);
            color: white;
        }
        
        .delete-button {
            background: var(--ios-red);
            color: white;
        }
        
        .add-category {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 0 16px 16px;
            padding: 16px;
            text-align: center;
            border: 2px dashed var(--ios-separator);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .add-category:hover {
            border-color: var(--ios-blue);
            background: var(--ios-gray6);
        }
        
        .add-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            background: var(--ios-gray5);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 20px;
            color: var(--ios-gray);
        }
        
        .add-text {
            font-size: 16px;
            color: var(--ios-secondary-label);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="record.html" class="navbar-button">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </div>
                <div class="navbar-title">分类管理</div>
                <div class="navbar-right">
                    <a href="add-category.html" class="navbar-button">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 类型选择器 -->
                <div class="type-selector">
                    <div class="type-option active" data-type="expense">支出分类</div>
                    <div class="type-option" data-type="income">收入分类</div>
                </div>

                <!-- 分类列表 -->
                <div class="category-list" id="categoryList">
                    <!-- 分类将通过JavaScript动态加载 -->
                </div>

                <!-- 添加分类 -->
                <div class="add-category" onclick="location.href='add-category.html'">
                    <div class="add-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="add-text">添加新分类</div>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="data-manager.js"></script>
    <script>
        let currentType = 'expense';

        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
            bindEvents();
        });

        function bindEvents() {
            // 类型切换
            const typeOptions = document.querySelectorAll('.type-option');
            typeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    typeOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    currentType = this.getAttribute('data-type');
                    loadCategories();
                });
            });
        }

        function loadCategories() {
            const categoryList = document.getElementById('categoryList');
            const categories = dataManager.getCategories(currentType);
            
            categoryList.innerHTML = '';
            
            categories.forEach(category => {
                const categoryElement = document.createElement('div');
                categoryElement.className = 'category-item';
                categoryElement.innerHTML = `
                    <div class="category-icon" style="background: ${category.color};">
                        <i class="${category.icon}"></i>
                    </div>
                    <div class="category-info">
                        <div class="category-name">${category.name}</div>
                        <div class="category-budget">
                            ${category.type === 'expense' && category.budget > 0 ? 
                                `预算: ¥${category.budget}` : 
                                (category.isDefault ? '系统分类' : '自定义分类')
                            }
                        </div>
                    </div>
                    <div class="category-actions">
                        ${!category.isDefault ? `
                            <button class="action-button edit-button" onclick="editCategory('${category.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-button delete-button" onclick="deleteCategory('${category.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                `;
                
                categoryList.appendChild(categoryElement);
            });
        }

        function editCategory(categoryId) {
            // 跳转到编辑分类页面
            window.location.href = `edit-category.html?id=${categoryId}`;
        }

        function deleteCategory(categoryId) {
            const category = dataManager.getCategories().find(cat => cat.id === categoryId);
            if (!category) return;

            if (confirm(`确定要删除分类"${category.name}"吗？\n删除后相关的交易记录将无法正常显示分类信息。`)) {
                try {
                    // 这里应该调用dataManager的删除方法
                    // 暂时用简单的方式处理
                    const categoryIndex = dataManager.data.categories.findIndex(cat => cat.id === categoryId);
                    if (categoryIndex !== -1) {
                        dataManager.data.categories.splice(categoryIndex, 1);
                        dataManager.saveData();
                        loadCategories();
                        alert('分类已删除');
                    }
                } catch (error) {
                    alert('删除失败，请重试');
                    console.error('删除分类失败:', error);
                }
            }
        }

        // 监听数据变化
        window.addEventListener('dataChanged', function() {
            loadCategories();
        });
    </script>
</body>
</html>
