// 财富管家数据管理系统
class DataManager {
    constructor() {
        this.initializeData();
        this.loadData();
    }

    // 初始化默认数据
    initializeData() {
        this.defaultData = {
            accounts: [
                {
                    id: '1',
                    name: '工商银行',
                    type: 'bank_card',
                    balance: 8650.50,
                    icon: 'fas fa-university',
                    color: '#3498DB',
                    cardNumber: '****1234',
                    isDefault: true,
                    isActive: true,
                    createdAt: new Date('2024-01-01')
                },
                {
                    id: '2',
                    name: '支付宝',
                    type: 'e_wallet',
                    balance: 2350.00,
                    icon: 'fas fa-mobile-alt',
                    color: '#1ABC9C',
                    cardNumber: '',
                    isDefault: false,
                    isActive: true,
                    createdAt: new Date('2024-01-01')
                },
                {
                    id: '3',
                    name: '微信钱包',
                    type: 'e_wallet',
                    balance: 1280.00,
                    icon: 'fas fa-comment-dollar',
                    color: '#2ECC71',
                    cardNumber: '',
                    isDefault: false,
                    isActive: true,
                    createdAt: new Date('2024-01-01')
                },
                {
                    id: '4',
                    name: '招商银行信用卡',
                    type: 'credit_card',
                    balance: -1200.00,
                    icon: 'fas fa-credit-card',
                    color: '#E74C3C',
                    cardNumber: '****5678',
                    isDefault: false,
                    isActive: true,
                    createdAt: new Date('2024-01-01')
                },
                {
                    id: '5',
                    name: '现金',
                    type: 'cash',
                    balance: 1500.00,
                    icon: 'fas fa-wallet',
                    color: '#F39C12',
                    cardNumber: '',
                    isDefault: false,
                    isActive: true,
                    createdAt: new Date('2024-01-01')
                }
            ],
            categories: [
                // 支出分类
                { id: '1', name: '餐饮', icon: 'fas fa-utensils', color: '#FF6B6B', type: 'expense', budget: 1200, isDefault: true },
                { id: '2', name: '交通', icon: 'fas fa-subway', color: '#4ECDC4', type: 'expense', budget: 500, isDefault: true },
                { id: '3', name: '购物', icon: 'fas fa-shopping-bag', color: '#45B7D1', type: 'expense', budget: 800, isDefault: true },
                { id: '4', name: '娱乐', icon: 'fas fa-gamepad', color: '#96CEB4', type: 'expense', budget: 600, isDefault: true },
                { id: '5', name: '医疗', icon: 'fas fa-heartbeat', color: '#FFEAA7', type: 'expense', budget: 300, isDefault: true },
                { id: '6', name: '教育', icon: 'fas fa-graduation-cap', color: '#DDA0DD', type: 'expense', budget: 400, isDefault: true },
                { id: '7', name: '居住', icon: 'fas fa-home', color: '#98D8C8', type: 'expense', budget: 2000, isDefault: true },
                { id: '8', name: '其他', icon: 'fas fa-ellipsis-h', color: '#8E8E93', type: 'expense', budget: 200, isDefault: true },
                // 收入分类
                { id: '9', name: '工资', icon: 'fas fa-money-bill-wave', color: '#34C759', type: 'income', budget: 0, isDefault: true },
                { id: '10', name: '奖金', icon: 'fas fa-gift', color: '#32D74B', type: 'income', budget: 0, isDefault: true },
                { id: '11', name: '投资收益', icon: 'fas fa-chart-line', color: '#30D158', type: 'income', budget: 0, isDefault: true },
                { id: '12', name: '其他收入', icon: 'fas fa-plus-circle', color: '#2DD55B', type: 'income', budget: 0, isDefault: true }
            ],
            transactions: [
                {
                    id: '1',
                    amount: 35.00,
                    type: 'expense',
                    categoryId: '1',
                    accountId: '5',
                    note: '午餐',
                    date: new Date().toISOString(),
                    createdAt: new Date().toISOString()
                },
                {
                    id: '2',
                    amount: 6.00,
                    type: 'expense',
                    categoryId: '2',
                    accountId: '2',
                    note: '地铁',
                    date: new Date().toISOString(),
                    createdAt: new Date().toISOString()
                },
                {
                    id: '3',
                    amount: 8500.00,
                    type: 'income',
                    categoryId: '9',
                    accountId: '1',
                    note: '工资',
                    date: new Date(Date.now() - ********).toISOString(),
                    createdAt: new Date(Date.now() - ********).toISOString()
                },
                {
                    id: '4',
                    amount: 28.00,
                    type: 'expense',
                    categoryId: '1',
                    accountId: '2',
                    note: '咖啡',
                    date: new Date(Date.now() - ********).toISOString(),
                    createdAt: new Date(Date.now() - ********).toISOString()
                }
            ],
            budgets: [
                {
                    id: '1',
                    name: '月度预算',
                    totalAmount: 5000,
                    period: 'monthly',
                    startDate: new Date(2024, 0, 1).toISOString(),
                    endDate: new Date(2024, 0, 31).toISOString(),
                    isActive: true
                }
            ],
            savingsGoals: [
                {
                    id: '1',
                    name: '旅行基金',
                    targetAmount: 10000,
                    currentAmount: 4500,
                    targetDate: new Date(2024, 11, 31).toISOString(),
                    description: '年底旅行计划',
                    icon: 'fas fa-plane',
                    isCompleted: false,
                    createdAt: new Date().toISOString()
                },
                {
                    id: '2',
                    name: '应急基金',
                    targetAmount: 20000,
                    currentAmount: 5000,
                    targetDate: new Date(2025, 5, 30).toISOString(),
                    description: '应急储备金',
                    icon: 'fas fa-shield-alt',
                    isCompleted: false,
                    createdAt: new Date().toISOString()
                }
            ]
        };
    }

    // 从本地存储加载数据
    loadData() {
        const savedData = localStorage.getItem('wealthkeeper_data');
        if (savedData) {
            this.data = JSON.parse(savedData);
        } else {
            this.data = JSON.parse(JSON.stringify(this.defaultData));
            this.saveData();
        }
    }

    // 保存数据到本地存储
    saveData() {
        localStorage.setItem('wealthkeeper_data', JSON.stringify(this.data));
        this.notifyDataChange();
    }

    // 数据变更通知
    notifyDataChange() {
        window.dispatchEvent(new CustomEvent('dataChanged', { detail: this.data }));
    }

    // 获取所有账户
    getAccounts() {
        return this.data.accounts.filter(account => account.isActive);
    }

    // 获取账户详情
    getAccount(id) {
        return this.data.accounts.find(account => account.id === id);
    }

    // 添加账户
    addAccount(accountData) {
        const newAccount = {
            id: Date.now().toString(),
            ...accountData,
            createdAt: new Date().toISOString(),
            isActive: true
        };
        this.data.accounts.push(newAccount);
        this.saveData();
        return newAccount;
    }

    // 更新账户
    updateAccount(id, updates) {
        const accountIndex = this.data.accounts.findIndex(account => account.id === id);
        if (accountIndex !== -1) {
            this.data.accounts[accountIndex] = { ...this.data.accounts[accountIndex], ...updates };
            this.saveData();
            return this.data.accounts[accountIndex];
        }
        return null;
    }

    // 获取所有分类
    getCategories(type = null) {
        if (type) {
            return this.data.categories.filter(category => category.type === type);
        }
        return this.data.categories;
    }

    // 添加分类
    addCategory(categoryData) {
        const newCategory = {
            id: Date.now().toString(),
            ...categoryData,
            isDefault: false,
            createdAt: new Date().toISOString()
        };
        this.data.categories.push(newCategory);
        this.saveData();
        return newCategory;
    }

    // 添加交易记录
    addTransaction(transactionData) {
        const newTransaction = {
            id: Date.now().toString(),
            ...transactionData,
            createdAt: new Date().toISOString()
        };
        
        // 更新账户余额
        const account = this.getAccount(transactionData.accountId);
        if (account) {
            if (transactionData.type === 'income') {
                account.balance += transactionData.amount;
            } else if (transactionData.type === 'expense') {
                account.balance -= transactionData.amount;
            }
            this.updateAccount(account.id, { balance: account.balance });
        }
        
        this.data.transactions.push(newTransaction);
        this.saveData();
        return newTransaction;
    }

    // 获取交易记录
    getTransactions(filters = {}) {
        let transactions = [...this.data.transactions];
        
        if (filters.type) {
            transactions = transactions.filter(t => t.type === filters.type);
        }
        
        if (filters.categoryId) {
            transactions = transactions.filter(t => t.categoryId === filters.categoryId);
        }
        
        if (filters.accountId) {
            transactions = transactions.filter(t => t.accountId === filters.accountId);
        }
        
        if (filters.startDate && filters.endDate) {
            transactions = transactions.filter(t => {
                const transactionDate = new Date(t.date);
                return transactionDate >= new Date(filters.startDate) && 
                       transactionDate <= new Date(filters.endDate);
            });
        }
        
        return transactions.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    // 获取统计数据
    getStatistics(period = 'month') {
        const now = new Date();
        let startDate, endDate;
        
        switch (period) {
            case 'week':
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
                endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay() + 6);
                break;
            case 'month':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                break;
            case 'year':
                startDate = new Date(now.getFullYear(), 0, 1);
                endDate = new Date(now.getFullYear(), 11, 31);
                break;
        }
        
        const transactions = this.getTransactions({
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
        });
        
        const income = transactions
            .filter(t => t.type === 'income')
            .reduce((sum, t) => sum + t.amount, 0);
            
        const expense = transactions
            .filter(t => t.type === 'expense')
            .reduce((sum, t) => sum + t.amount, 0);
        
        return {
            income,
            expense,
            balance: income - expense,
            transactions: transactions.length
        };
    }

    // 获取总资产
    getTotalAssets() {
        return this.data.accounts
            .filter(account => account.isActive)
            .reduce((total, account) => total + account.balance, 0);
    }

    // 重置数据
    resetData() {
        this.data = JSON.parse(JSON.stringify(this.defaultData));
        this.saveData();
    }
}

// 全局数据管理器实例
window.dataManager = new DataManager();
