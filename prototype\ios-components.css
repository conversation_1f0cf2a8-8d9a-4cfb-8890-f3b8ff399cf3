/* iOS Components CSS - 财富管家 App */

/* 基础变量 */
:root {
  --ios-blue: #007AFF;
  --ios-green: #34C759;
  --ios-red: #FF3B30;
  --ios-orange: #FF9500;
  --ios-purple: #AF52DE;
  --ios-gray: #8E8E93;
  --ios-gray2: #AEAEB2;
  --ios-gray3: #C7C7CC;
  --ios-gray4: #D1D1D6;
  --ios-gray5: #E5E5EA;
  --ios-gray6: #F2F2F7;
  --ios-background: #F2F2F7;
  --ios-white: #FFFFFF;
  --ios-black: #000000;
  --ios-label: #000000;
  --ios-secondary-label: #3C3C43;
  --ios-tertiary-label: #3C3C4399;
  --ios-separator: #3C3C4349;
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
  background-color: var(--ios-background);
  color: var(--ios-label);
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
}

/* iPhone 15 容器 */
.iphone-container {
  width: 393px;
  height: 852px;
  margin: 20px auto;
  background: #000;
  border-radius: 47px;
  padding: 2px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  position: relative;
}

.iphone-screen {
  width: 100%;
  height: 100%;
  background: var(--ios-background);
  border-radius: 45px;
  overflow: hidden;
  position: relative;
}

/* 状态栏 */
.status-bar {
  height: 54px;
  background: var(--ios-white);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  font-size: 17px;
  font-weight: 600;
  position: relative;
  z-index: 100;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 5px;
}

.battery {
  width: 24px;
  height: 12px;
  border: 1px solid var(--ios-label);
  border-radius: 2px;
  position: relative;
}

.battery::after {
  content: '';
  position: absolute;
  right: -3px;
  top: 3px;
  width: 2px;
  height: 6px;
  background: var(--ios-label);
  border-radius: 0 1px 1px 0;
}

.battery-fill {
  height: 100%;
  background: var(--ios-green);
  border-radius: 1px;
  width: 80%;
}

/* 导航栏 */
.navbar {
  height: 44px;
  background: var(--ios-white);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 0.5px solid var(--ios-separator);
  position: relative;
}

.navbar-title {
  font-size: 17px;
  font-weight: 600;
  color: var(--ios-label);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.navbar-left, .navbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1;
}

.navbar-button {
  color: var(--ios-blue);
  font-size: 17px;
  text-decoration: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  margin: -8px;
}

.navbar-button:hover {
  opacity: 0.6;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  background: var(--ios-background);
  padding-bottom: 34px; /* 为home indicator留空间 */
}

/* 底部标签栏 */
.tab-bar {
  height: 83px;
  background: var(--ios-white);
  border-top: 0.5px solid var(--ios-separator);
  display: flex;
  align-items: flex-start;
  padding-top: 8px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--ios-gray);
  font-size: 10px;
  padding: 4px 0;
}

.tab-item.active {
  color: var(--ios-blue);
}

.tab-icon {
  font-size: 24px;
  margin-bottom: 2px;
}

/* Home Indicator */
.home-indicator {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 134px;
  height: 5px;
  background: var(--ios-label);
  border-radius: 3px;
  opacity: 0.3;
}

/* 卡片组件 */
.card {
  background: var(--ios-white);
  border-radius: 12px;
  margin: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 16px;
  border-bottom: 0.5px solid var(--ios-separator);
}

.card-title {
  font-size: 17px;
  font-weight: 600;
  color: var(--ios-label);
}

.card-content {
  padding: 16px;
}

/* 列表组件 */
.list-group {
  background: var(--ios-white);
  border-radius: 12px;
  margin: 16px;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 0.5px solid var(--ios-separator);
  text-decoration: none;
  color: var(--ios-label);
  min-height: 44px;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background: var(--ios-gray6);
}

.list-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: var(--ios-white);
}

.list-content {
  flex: 1;
}

.list-title {
  font-size: 17px;
  font-weight: 400;
  color: var(--ios-label);
}

.list-subtitle {
  font-size: 15px;
  color: var(--ios-secondary-label);
  margin-top: 2px;
}

.list-value {
  font-size: 17px;
  color: var(--ios-secondary-label);
  margin-left: auto;
}

.list-chevron {
  margin-left: 8px;
  color: var(--ios-gray2);
  font-size: 14px;
}

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 17px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  min-height: 44px;
  transition: opacity 0.2s;
}

.btn:hover {
  opacity: 0.8;
}

.btn-primary {
  background: var(--ios-blue);
  color: var(--ios-white);
}

.btn-success {
  background: var(--ios-green);
  color: var(--ios-white);
}

.btn-danger {
  background: var(--ios-red);
  color: var(--ios-white);
}

.btn-secondary {
  background: var(--ios-gray5);
  color: var(--ios-label);
}

.btn-large {
  width: 100%;
  margin: 16px;
  padding: 16px 24px;
  font-size: 18px;
}

/* 输入组件 */
.form-group {
  margin: 16px;
}

.form-label {
  font-size: 15px;
  color: var(--ios-secondary-label);
  margin-bottom: 8px;
  display: block;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--ios-separator);
  border-radius: 12px;
  font-size: 17px;
  background: var(--ios-white);
  color: var(--ios-label);
}

.form-control:focus {
  outline: none;
  border-color: var(--ios-blue);
}

/* 进度条 */
.progress {
  height: 8px;
  background: var(--ios-gray5);
  border-radius: 4px;
  overflow: hidden;
  margin: 8px 0;
}

.progress-bar {
  height: 100%;
  background: var(--ios-blue);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* 徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
}

.badge-primary {
  background: var(--ios-blue);
  color: var(--ios-white);
}

.badge-success {
  background: var(--ios-green);
  color: var(--ios-white);
}

.badge-danger {
  background: var(--ios-red);
  color: var(--ios-white);
}

/* 浮动按钮 */
.fab {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: var(--ios-blue);
  color: var(--ios-white);
  border: none;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab:hover {
  transform: scale(1.05);
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-primary { color: var(--ios-blue); }
.text-success { color: var(--ios-green); }
.text-danger { color: var(--ios-red); }
.text-secondary { color: var(--ios-secondary-label); }
.text-large { font-size: 24px; font-weight: 600; }
.text-small { font-size: 13px; }

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }

.d-flex { display: flex; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }
.flex-1 { flex: 1; }

/* 响应式 */
@media (max-width: 414px) {
  .iphone-container {
    width: 100%;
    height: 100vh;
    margin: 0;
    border-radius: 0;
    padding: 0;
  }
  
  .iphone-screen {
    border-radius: 0;
  }
}
