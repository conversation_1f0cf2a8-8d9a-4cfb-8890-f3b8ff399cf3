<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财富管家 - 账户</title>
    <link rel="stylesheet" href="ios-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .total-assets {
            background: linear-gradient(135deg, var(--ios-green), #2ECC71);
            color: white;
            margin: 16px;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
        }
        
        .assets-label {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 8px;
        }
        
        .assets-amount {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .assets-change {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .account-list {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 0 16px 16px;
            overflow: hidden;
        }
        
        .account-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid var(--ios-separator);
            text-decoration: none;
            color: var(--ios-label);
        }
        
        .account-item:last-child {
            border-bottom: none;
        }
        
        .account-item:hover {
            background: var(--ios-gray6);
        }
        
        .account-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            color: white;
        }
        
        .account-info {
            flex: 1;
        }
        
        .account-name {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .account-type {
            font-size: 14px;
            color: var(--ios-secondary-label);
        }
        
        .account-balance {
            text-align: right;
        }
        
        .balance-amount {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .balance-change {
            font-size: 12px;
            color: var(--ios-secondary-label);
        }
        
        .add-account {
            background: var(--ios-white);
            border-radius: 12px;
            margin: 0 16px 16px;
            padding: 16px;
            text-align: center;
            border: 2px dashed var(--ios-separator);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .add-account:hover {
            border-color: var(--ios-blue);
            background: var(--ios-gray6);
        }
        
        .add-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            background: var(--ios-gray5);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 20px;
            color: var(--ios-gray);
        }
        
        .add-text {
            font-size: 16px;
            color: var(--ios-secondary-label);
        }
        
        .account-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin: 0 16px 16px;
        }
        
        .stat-card {
            background: var(--ios-white);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--ios-secondary-label);
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin: 0 16px 16px;
        }
        
        .action-button {
            background: var(--ios-white);
            border: none;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .action-button:hover {
            transform: scale(0.98);
        }
        
        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 18px;
            color: white;
        }
        
        .action-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--ios-label);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="navbar">
                <div class="navbar-left">
                    <a href="#" class="navbar-button">
                        <i class="fas fa-sync"></i>
                    </a>
                </div>
                <div class="navbar-title">账户</div>
                <div class="navbar-right">
                    <a href="add-account.html" class="navbar-button">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 总资产 -->
                <div class="total-assets">
                    <div class="assets-label">总资产</div>
                    <div class="assets-amount">¥12,580.50</div>
                    <div class="assets-change">较昨日 +¥156.00</div>
                </div>

                <!-- 账户统计 -->
                <div class="account-stats">
                    <div class="stat-card">
                        <div class="stat-value text-primary">5</div>
                        <div class="stat-label">账户数量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value text-success">¥8,500</div>
                        <div class="stat-label">本月收入</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value text-danger">¥6,350</div>
                        <div class="stat-label">本月支出</div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="quick-actions">
                    <button class="action-button" onclick="location.href='account-manage.html'">
                        <div class="action-icon" style="background: var(--ios-blue);">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="action-title">账户管理</div>
                    </button>
                    <button class="action-button" onclick="location.href='bill-import.html'">
                        <div class="action-icon" style="background: var(--ios-green);">
                            <i class="fas fa-file-import"></i>
                        </div>
                        <div class="action-title">账单导入</div>
                    </button>
                </div>

                <!-- 账户列表 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">我的账户</div>
                    </div>
                </div>
                
                <div class="account-list">
                    <a href="account-details.html?id=1" class="account-item">
                        <div class="account-icon" style="background: #3498DB;">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="account-info">
                            <div class="account-name">工商银行</div>
                            <div class="account-type">储蓄卡 ****1234</div>
                        </div>
                        <div class="account-balance">
                            <div class="balance-amount">¥8,650.50</div>
                            <div class="balance-change">+¥500.00</div>
                        </div>
                    </a>
                    
                    <a href="account-details.html?id=2" class="account-item">
                        <div class="account-icon" style="background: #1ABC9C;">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="account-info">
                            <div class="account-name">支付宝</div>
                            <div class="account-type">电子钱包</div>
                        </div>
                        <div class="account-balance">
                            <div class="balance-amount">¥2,350.00</div>
                            <div class="balance-change">-¥120.00</div>
                        </div>
                    </a>
                    
                    <a href="account-details.html?id=3" class="account-item">
                        <div class="account-icon" style="background: #2ECC71;">
                            <i class="fas fa-comment-dollar"></i>
                        </div>
                        <div class="account-info">
                            <div class="account-name">微信钱包</div>
                            <div class="account-type">电子钱包</div>
                        </div>
                        <div class="account-balance">
                            <div class="balance-amount">¥1,280.00</div>
                            <div class="balance-change">-¥50.00</div>
                        </div>
                    </a>
                    
                    <a href="account-details.html?id=4" class="account-item">
                        <div class="account-icon" style="background: #E74C3C;">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="account-info">
                            <div class="account-name">招商银行信用卡</div>
                            <div class="account-type">信用卡 ****5678</div>
                        </div>
                        <div class="account-balance">
                            <div class="balance-amount">-¥1,200.00</div>
                            <div class="balance-change">-¥300.00</div>
                        </div>
                    </a>
                    
                    <a href="account-details.html?id=5" class="account-item">
                        <div class="account-icon" style="background: #F39C12;">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="account-info">
                            <div class="account-name">现金</div>
                            <div class="account-type">现金账户</div>
                        </div>
                        <div class="account-balance">
                            <div class="balance-amount">¥1,500.00</div>
                            <div class="balance-change">-¥200.00</div>
                        </div>
                    </a>
                </div>

                <!-- 添加账户 -->
                <div class="add-account" onclick="location.href='add-account.html'">
                    <div class="add-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="add-text">添加新账户</div>
                </div>

                <!-- 更多功能 -->
                <div class="list-group">
                    <a href="transfer-history.html" class="list-item">
                        <div class="list-icon" style="background: var(--ios-purple);">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">转账记录</div>
                            <div class="list-subtitle">查看账户间转账历史</div>
                        </div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                    <a href="account-settings.html" class="list-item">
                        <div class="list-icon" style="background: var(--ios-gray);">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="list-content">
                            <div class="list-title">账户设置</div>
                            <div class="list-subtitle">管理账户显示和排序</div>
                        </div>
                        <i class="fas fa-chevron-right list-chevron"></i>
                    </a>
                </div>
            </div>

            <!-- 底部标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-home"></i></div>
                    <div>首页</div>
                </a>
                <a href="record.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-plus-circle"></i></div>
                    <div>记账</div>
                </a>
                <a href="statistics.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-chart-bar"></i></div>
                    <div>统计</div>
                </a>
                <a href="budget.html" class="tab-item">
                    <div class="tab-icon"><i class="fas fa-wallet"></i></div>
                    <div>预算</div>
                </a>
                <a href="account.html" class="tab-item active">
                    <div class="tab-icon"><i class="fas fa-credit-card"></i></div>
                    <div>账户</div>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签栏切换
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const href = this.getAttribute('href');
                    if (href && href !== '#') {
                        window.location.href = href;
                    }
                });
            });

            // 账户项点击效果
            const accountItems = document.querySelectorAll('.account-item');
            accountItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    this.style.backgroundColor = 'var(--ios-gray6)';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 150);
                });
            });

            // 添加账户点击效果
            const addAccount = document.querySelector('.add-account');
            addAccount.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });

            // 快速操作按钮点击效果
            const actionButtons = document.querySelectorAll('.action-button');
            actionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
